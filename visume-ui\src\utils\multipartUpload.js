/**
 * MultipartVideoUpload - Utility class for handling S3 multipart video uploads
 * 
 * Features:
 * - File chunking with configurable chunk size
 * - Parallel uploads with concurrency control
 * - Progress tracking and error handling
 * - Retry logic for failed parts
 * - Upload resumption capability
 */

export class MultipartVideoUpload {
  constructor(file, options = {}) {
    this.file = file;
    this.fileName = options.fileName || 'video';
    this.chunkSize = options.chunkSize || 10 * 1024 * 1024; // 10MB default
    this.maxConcurrency = options.maxConcurrency || 3; // 3 parallel uploads
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000; // 1 second base delay
    
    // Callbacks
    this.onProgress = options.onProgress || (() => {});
    this.onError = options.onError || (() => {});
    this.onPartComplete = options.onPartComplete || (() => {});
    
    // Internal state
    this.uploadId = null;
    this.key = null;
    this.parts = [];
    this.uploadedParts = [];
    this.activeUploads = new Map();
    this.aborted = false;
    this.totalParts = 0;
    this.uploadedBytes = 0;
    
    // Calculate total parts
    this.totalParts = Math.ceil(this.file.size / this.chunkSize);
    
    console.log('MultipartVideoUpload initialized:', {
      fileSize: this.file.size,
      chunkSize: this.chunkSize,
      totalParts: this.totalParts,
      fileName: this.fileName
    });
  }

  /**
   * Start the multipart upload process
   */
  async start() {
    try {
      console.log('Starting multipart upload...');
      
      // Step 1: Initialize multipart upload
      await this.initializeUpload();
      
      // Step 2: Upload all parts in parallel
      await this.uploadParts();
      
      // Step 3: Complete the multipart upload
      const finalUrl = await this.completeUpload();
      
      console.log('Multipart upload completed successfully:', finalUrl);
      return finalUrl;
      
    } catch (error) {
      console.error('Multipart upload failed:', error);
      
      // Attempt to abort the upload on failure
      if (this.uploadId) {
        try {
          await this.abortUpload();
        } catch (abortError) {
          console.error('Failed to abort upload:', abortError);
        }
      }
      
      this.onError(error);
      throw error;
    }
  }

  /**
   * Initialize multipart upload session
   */
  async initializeUpload() {
    const host = import.meta.env.VITE_APP_HOST || "https://api.zoomjobs.in";
    
    const response = await fetch(`${host}/api/v1/multipart/init`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        fileName: this.fileName,
        contentType: this.file.type || 'video/webm',
        fileSize: this.file.size
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to initialize multipart upload: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    this.uploadId = data.uploadId;
    this.key = data.key;
    
    console.log('Multipart upload initialized:', {
      uploadId: this.uploadId,
      key: this.key
    });
  }

  /**
   * Upload all parts with concurrency control
   */
  async uploadParts() {
    const uploadPromises = [];
    let activeConcurrency = 0;
    
    for (let partNumber = 1; partNumber <= this.totalParts; partNumber++) {
      // Wait if we've reached max concurrency
      while (activeConcurrency >= this.maxConcurrency) {
        await new Promise(resolve => setTimeout(resolve, 100));
        activeConcurrency = this.activeUploads.size;
      }
      
      if (this.aborted) {
        throw new Error('Upload aborted by user');
      }
      
      const uploadPromise = this.uploadPart(partNumber)
        .finally(() => {
          this.activeUploads.delete(partNumber);
        });
      
      this.activeUploads.set(partNumber, uploadPromise);
      uploadPromises.push(uploadPromise);
      activeConcurrency++;
    }
    
    // Wait for all parts to complete
    const results = await Promise.allSettled(uploadPromises);
    
    // Check for any failed uploads
    const failures = results.filter(result => result.status === 'rejected');
    if (failures.length > 0) {
      const firstFailure = failures[0];
      throw new Error(`Part upload failed: ${firstFailure.reason.message}`);
    }
    
    console.log(`All ${this.totalParts} parts uploaded successfully`);
  }

  /**
   * Upload a single part with retry logic
   */
  async uploadPart(partNumber, retryCount = 0) {
    try {
      // Calculate part boundaries
      const start = (partNumber - 1) * this.chunkSize;
      const end = Math.min(start + this.chunkSize, this.file.size);
      const chunk = this.file.slice(start, end);
      
      console.log(`Uploading part ${partNumber}/${this.totalParts} (${chunk.size} bytes)`);
      
      // Get pre-signed URL for this part
      const partUrl = await this.getPartUploadUrl(partNumber);
      
      // Upload the chunk
      const response = await fetch(partUrl, {
        method: 'PUT',
        body: chunk,
        headers: {
          'Content-Type': this.file.type || 'video/webm',
          'Content-Length': chunk.size.toString()
        }
      });
      
      if (!response.ok) {
        throw new Error(`Part ${partNumber} upload failed: ${response.status} ${response.statusText}`);
      }

      // Log all response headers for debugging
      console.log(`Part ${partNumber} response headers:`, Array.from(response.headers.entries()));

      // Extract ETag from response headers
      const etag = response.headers.get('ETag');
      if (!etag) {
        // Enhanced error logging for debugging
        console.error(`Part ${partNumber} upload succeeded but no ETag received`);
        console.error('Available response headers:', Array.from(response.headers.entries()));
        console.error('Response status:', response.status, response.statusText);
        console.error('Response URL:', response.url);
        throw new Error(`Part ${partNumber} upload succeeded but no ETag received. Check CORS configuration and S3 bucket settings.`);
      }
      
      // Store the completed part info
      const partInfo = {
        PartNumber: partNumber,
        ETag: etag.replace(/"/g, '') // Remove quotes from ETag
      };
      
      this.uploadedParts.push(partInfo);
      this.uploadedBytes += chunk.size;
      
      // Update progress
      const progress = (this.uploadedBytes / this.file.size) * 100;
      this.onProgress({
        percentage: progress,
        uploadedBytes: this.uploadedBytes,
        totalBytes: this.file.size,
        completedParts: this.uploadedParts.length,
        totalParts: this.totalParts
      });
      
      this.onPartComplete(partNumber, partInfo);
      
      console.log(`Part ${partNumber} uploaded successfully (ETag: ${etag})`);
      return partInfo;
      
    } catch (error) {
      console.error(`Part ${partNumber} upload failed (attempt ${retryCount + 1}):`, error);
      
      // Retry logic
      if (retryCount < this.maxRetries && !this.aborted) {
        const delay = this.retryDelay * Math.pow(2, retryCount); // Exponential backoff
        console.log(`Retrying part ${partNumber} in ${delay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.uploadPart(partNumber, retryCount + 1);
      }
      
      throw error;
    }
  }

  /**
   * Get pre-signed URL for uploading a specific part
   */
  async getPartUploadUrl(partNumber) {
    const host = import.meta.env.VITE_APP_HOST || "https://api.zoomjobs.in";
    
    const response = await fetch(
      `${host}/api/v1/multipart/part-url/${this.uploadId}/${partNumber}?key=${encodeURIComponent(this.key)}`,
      {
        method: 'GET',
        credentials: 'include'
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to get part upload URL: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    return data.url;
  }

  /**
   * Complete multipart upload by combining all parts
   */
  async completeUpload() {
    const host = import.meta.env.VITE_APP_HOST || "https://api.zoomjobs.in";
    
    // Sort parts by part number
    const sortedParts = this.uploadedParts.sort((a, b) => a.PartNumber - b.PartNumber);
    
    const response = await fetch(`${host}/api/v1/multipart/complete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        uploadId: this.uploadId,
        key: this.key,
        parts: sortedParts
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to complete multipart upload: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    console.log('Multipart upload completed:', data);
    
    return data.url;
  }

  /**
   * Abort multipart upload and cleanup
   */
  async abortUpload() {
    if (!this.uploadId) return;
    
    const host = import.meta.env.VITE_APP_HOST || "https://api.zoomjobs.in";
    
    try {
      const response = await fetch(`${host}/api/v1/multipart/abort/${this.uploadId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          key: this.key
        })
      });

      if (!response.ok) {
        console.warn('Failed to abort multipart upload:', response.statusText);
      } else {
        console.log('Multipart upload aborted successfully');
      }
    } catch (error) {
      console.warn('Error aborting multipart upload:', error);
    }
  }

  /**
   * Abort the upload process
   */
  abort() {
    console.log('Aborting multipart upload...');
    this.aborted = true;
    
    // Cancel all active uploads
    this.activeUploads.forEach((promise, partNumber) => {
      console.log(`Cancelling upload for part ${partNumber}`);
    });
    
    // Clear active uploads
    this.activeUploads.clear();
    
    // Abort the multipart upload on S3
    this.abortUpload().catch(error => {
      console.error('Error during abort cleanup:', error);
    });
  }
}

/**
 * Utility function to determine if multipart upload should be used
 */
export function shouldUseMultipartUpload(fileSize, threshold = 10 * 1024 * 1024) {
  return fileSize > threshold;
}

/**
 * Factory function to create appropriate uploader based on file size
 */
export function createVideoUploader(file, options = {}) {
  const useMultipart = shouldUseMultipartUpload(file.size, options.multipartThreshold);
  
  console.log(`File size: ${file.size} bytes, using ${useMultipart ? 'multipart' : 'single'} upload`);
  
  if (useMultipart) {
    return new MultipartVideoUpload(file, options);
  }
  
  // Return null for single upload - will use existing logic
  return null;
}
