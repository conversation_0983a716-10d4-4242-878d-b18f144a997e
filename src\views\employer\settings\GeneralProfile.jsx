import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';
import { API_URL } from '../../../config/config';
import { FaUser, FaPhone, FaEnvelope, FaBriefcase } from 'react-icons/fa';

const GeneralProfile = () => {
  const [profileData, setProfileData] = useState({
    emp_name: '',
    emp_email: '',
    emp_mobile: '',
    designation: '',
    company_name: '', // Keep this field for display purposes only
    profile_picture: null
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [previewImage, setPreviewImage] = useState(null);
  
  // Get employer ID from localStorage
  const empId = localStorage.getItem('emp_id');
  
  useEffect(() => {
    fetchEmployerDetails();
  }, []);
  
  const fetchEmployerDetails = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_URL}/getEmployerDetails`, {
        headers: {
          Authorization: empId
        }
      });
      
      if (response.data.success) {
        const { emp_name, emp_email, emp_mobile, designation, company_name, profile_picture } = response.data.employer;
        
        setProfileData({
          emp_name: emp_name || '',
          emp_email: emp_email || '',
          emp_mobile: emp_mobile ? emp_mobile.toString() : '',
          designation: designation || '',
          company_name: company_name || '',
          profile_picture
        });
        
        if (profile_picture) {
          setPreviewImage(`${API_URL}/utils/files/profile_pics/${profile_picture}`);
        }
      }
    } catch (error) {
      console.error('Error fetching employer details:', error);
      toast.error('Failed to load profile information');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfileData({ ...profileData, [name]: value });
    
    // Clear error when field is updated
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };
  
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Preview the selected image
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
      
      setProfileData({ ...profileData, profile_picture: file });
    }
  };
  
  const validateForm = () => {
    const newErrors = {};
    
    if (!profileData.emp_name.trim()) {
      newErrors.emp_name = 'Name is required';
    }
    
    if (!profileData.emp_mobile) {
      newErrors.emp_mobile = 'Mobile number is required';
    } else if (!/^\d{10}$/.test(profileData.emp_mobile)) {
      newErrors.emp_mobile = 'Mobile number must be 10 digits';
    }
    
    if (!profileData.designation.trim()) {
      newErrors.designation = 'Designation is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const formData = new FormData();
      formData.append('emp_name', profileData.emp_name);
      formData.append('emp_email', profileData.emp_email);
      formData.append('emp_mobile', profileData.emp_mobile);
      formData.append('designation', profileData.designation);
      
      // Only append company_name if it exists (for backward compatibility)
      if (profileData.company_name) {
        formData.append('company_name', profileData.company_name);
      }
      
      // Only append profile picture if a new one was selected
      if (profileData.profile_picture && typeof profileData.profile_picture !== 'string') {
        formData.append('company_logo', profileData.profile_picture);
      }
      
      const response = await axios.put(`${API_URL}/updateEmployerProfile`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': empId
        }
      });
      
      if (response.data.success) {
        toast.success('Profile updated successfully');
        fetchEmployerDetails(); // Refresh data
      } else {
        toast.error(response.data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(error.response?.data?.message || 'Failed to update profile');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (isLoading) {
    return <div className="loading-container">Loading profile information...</div>;
  }
  
  return (
    <div className="general-profile-container">
      <div className="profile-header">
        <div className="profile-header-content">
          <h2>{profileData.emp_name || 'Your Profile'}</h2>
          <p className="subtitle">{profileData.company_name || profileData.designation}</p>
        </div>
      </div>
      
      <div className="profile-card">
        <div className="profile-picture-section">
          <div className="profile-picture-container">
            {previewImage ? (
              <img src={previewImage} alt="Profile" className="profile-picture" />
            ) : (
              <div className="profile-picture-placeholder">
                <FaUser size={40} />
              </div>
            )}
          </div>
          
          <div className="upload-button-container">
            <label htmlFor="profile-picture-upload" className="upload-button">
              Upload Photo
            </label>
            <input
              type="file"
              id="profile-picture-upload"
              accept="image/jpeg,image/png,image/jpg"
              onChange={handleImageChange}
              style={{ display: 'none' }}
            />
            <p className="upload-hint">JPG, PNG or JPEG (max 5MB)</p>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="profile-form">
          <div className="form-section">
            <h3 className="section-title">Personal Information</h3>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="emp_name">
                  <FaUser className="input-icon" /> Full Name
                </label>
                <input
                  type="text"
                  id="emp_name"
                  name="emp_name"
                  value={profileData.emp_name}
                  onChange={handleChange}
                  placeholder="Your full name"
                  className={errors.emp_name ? 'error' : ''}
                />
                {errors.emp_name && <span className="error-message">{errors.emp_name}</span>}
              </div>
              
              <div className="form-group">
                <label htmlFor="emp_mobile">
                  <FaPhone className="input-icon" /> Mobile Number
                </label>
                <input
                  type="tel"
                  id="emp_mobile"
                  name="emp_mobile"
                  value={profileData.emp_mobile}
                  onChange={handleChange}
                  placeholder="Your mobile number"
                  className={errors.emp_mobile ? 'error' : ''}
                />
                {errors.emp_mobile && <span className="error-message">{errors.emp_mobile}</span>}
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="emp_email">
                  <FaEnvelope className="input-icon" /> Email Address
                </label>
                <input
                  type="email"
                  id="emp_email"
                  name="emp_email"
                  value={profileData.emp_email}
                  onChange={handleChange}
                  placeholder="Your email address"
                  disabled // Email should not be editable
                  className="disabled-input"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="designation">
                  <FaBriefcase className="input-icon" /> Designation
                </label>
                <input
                  type="text"
                  id="designation"
                  name="designation"
                  value={profileData.designation}
                  onChange={handleChange}
                  placeholder="Your job title"
                  className={errors.designation ? 'error' : ''}
                />
                {errors.designation && <span className="error-message">{errors.designation}</span>}
              </div>
            </div>
          </div>
          
          <div className="form-actions">
            <button 
              type="submit" 
              className="save-button"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GeneralProfile;