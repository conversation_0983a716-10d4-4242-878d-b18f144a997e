import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

import { useNavigate } from "react-router-dom";

import ProfileCard from "../ProfilesUI/ProfileCard";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
const JobDescriptionCandidates = () => {
  const { state } = useLocation();
  const job = state?.job;
  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const [loadingId, setLoadingId] = useState(null);
  const [selectedProfiles, setSelectedProfiles] = useState(new Set());
  const [openTooltipId, setOpenTooltipId] = useState(null);

  const handleShortlist = async (video_profile_id, cand_id) => {
    if (loadingId === video_profile_id) return;
    setLoadingId(video_profile_id);
    try {
      const emp_id = job?.emp_id || Cookies.get("employerId");
      if (!emp_id) {
        alert("You need to be an employer to shortlist profiles");
        return;
      }
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: video_profile_id }),
        }
      );
      if (!response.ok) {
        const msg = await response.json();
        alert(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }
      const data = await response.json();
      toast.success(data.message || "Profile shortlisted!");
      setCandidates((prev) =>
        prev.filter(
          (cand) =>
            (cand.video_profile_id || cand.id || cand._id) !== video_profile_id
        )
      );
      setSelectedProfiles((prev) => {
        const newSelectedProfiles = new Set(prev);
        if (newSelectedProfiles.has(video_profile_id)) {
          newSelectedProfiles.delete(video_profile_id);
        } else {
          newSelectedProfiles.add(video_profile_id);
        }
        return newSelectedProfiles;
      });
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  useEffect(() => {
    const fetchCandidates = async () => {
      setLoading(true);
      try {
        if (!job) return;
        // Fetch candidates matching role and skills
        const candRes = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/filterCandidate?role=${encodeURIComponent(job.role)}`
        );
        const candData = await candRes.json();

        let arr = [];
        if (Array.isArray(candData.candidates)) arr = candData.candidates;
        else if (Array.isArray(candData.candidateProfiles))
          arr = candData.candidateProfiles;
        else if (Array.isArray(candData)) arr = candData;
        else arr = [];

        // Fetch shortlisted profiles for employer
        const emp_id = job?.emp_id || Cookies.get("employerId");
        let shortListedIds = [];
        if (emp_id) {
          try {
            const res = await fetch(
              `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
            );
            const data = await res.json();
            if (data.data && data.data.length) {
              shortListedIds = data.data.map((e) => e.video_profile_id);
            }
          } catch (err) {
            // ignore
          }
        }

        // Filter out already shortlisted candidates
        arr = arr.filter(
          (cand) =>
            !shortListedIds.includes(
              cand.video_profile_id || cand.id || cand._id
            )
        );

        setCandidates(arr);
      } catch (err) {
        setCandidates([]);
      } finally {
        setLoading(false);
      }
    };
    if (job) fetchCandidates();
  }, [job]);

  if (loading) return <div>Loading...</div>;
  if (!job) return <div>Job description not found.</div>;

  return (
    <div className="p-6">
      <div className="mb-6 flex flex-col gap-4 rounded-xl bg-gradient-to-br from-white via-gray-50 to-gray-100 p-6 shadow dark:from-navy-900 dark:via-navy-800 dark:to-navy-700">
        <div className="flex items-center gap-3">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {job.role}
          </h2>
        </div>
        <div className="flex flex-wrap gap-2">
          {(Array.isArray(job.skills) ? job.skills : [job.skills]).map(
            (skill, idx) => (
              <span
                key={idx}
                className="inline-flex items-center gap-1 rounded-full border border-blue-200 bg-blue-50 px-3 py-1 text-xs font-semibold text-blue-700"
              >
                {skill}
              </span>
            )
          )}
        </div>
        <div className="flex items-center gap-2">
          <span className="inline-flex items-center gap-1 rounded-full border border-green-200 bg-green-50 px-3 py-1 text-xs font-semibold text-green-700">
            {Array.isArray(job.location)
              ? job.location.join(", ")
              : job.location}
          </span>
        </div>
      </div>
      {Array.isArray(candidates) && candidates.length === 0 ? (
        <div className="py-8 text-center text-gray-500">
          No matching candidates found.
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {Array.isArray(candidates) &&
            candidates.map((cand) => {
              // Parse candidateDetails
              const details = Array.isArray(cand.candidateDetails)
                ? cand.candidateDetails[0]
                : {};
              // Parse salary
              let salary = {};
              try {
                salary = cand.salary ? JSON.parse(cand.salary) : {};
              } catch {}
              return (
                <ProfileCard
                  key={cand.id || cand._id}
                  {...cand}
                  candidateDetails={
                    Array.isArray(cand.candidateDetails)
                      ? cand.candidateDetails
                      : [cand.candidateDetails]
                  }
                  score={cand.score}
                  openTooltipId={openTooltipId}
                  setOpenTooltipId={setOpenTooltipId}
                  onShortlist={() => handleShortlist(cand.video_profile_id || cand.id || cand._id)}
                  isShortlisted={selectedProfiles.has(cand.video_profile_id || cand.id || cand._id)}
                  isLoading={loadingId === (cand.video_profile_id || cand.id || cand._id)}
                />
              );
            })}
        </div>
      )}
    </div>
  );
};

export default JobDescriptionCandidates;
