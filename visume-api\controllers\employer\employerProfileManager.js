// Employer Profile Management Functions

const prisma = require("../../config/prisma");
const { convertBigIntToNumber } = require("../../utils/bigintHelper");

// Fetch profiles by employer ID
exports.getProfilesByEmployerId = async (req, res) => {
  const { emp_id } = req.params;

  try {
    const profiles = await prisma.employerprofiles.findMany({
      where: { emp_id: Number(emp_id) },
    });

    if (!profiles || profiles.length === 0) {
      return res
        .status(404)
        .json({ message: "No profiles found for this employer." });
    }

    res.status(200).json({
      message: "Profiles fetched successfully.",
      data: convertBigIntToNumber(profiles),
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    res.status(500).send("Failed to fetch profiles.");
  }
};

// Get employer profile plan and candidate counts
exports.getEmployerProfilesData = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employerPlan = await prisma.employerplans.findFirst({
      where: { emp_id: Number(emp_id) },
      select: { plan_id: true, creditsLeft: true, end_date: true },
    });

    if (!employerPlan) {
      return res.status(404).json({
        message: "No employer profile plan found. Please log in again.",
      });
    }

    const planDetails = await prisma.plans.findUnique({
      where: { id: employerPlan.plan_id },
    });

    if (!planDetails) {
      return res.status(404).json({
        message: "No plan details found for the employer profile.",
      });
    }

    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
      select: { emp_name: true, profile_picture: true, company_id: true },
    });

    if (!employer) {
      return res.status(404).json({
        message: "Employer not found. Please log in again.",
      });
    }

    // Fetch company data if company_id exists
    let company = null;
    if (employer.company_id !== null && employer.company_id !== undefined) {
      company = await prisma.company.findUnique({
        where: { id: employer.company_id },
        select: { company_logo: true, company_name: true, company_website: true },
      });
    }

    const shortlisted_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "shortlisted" },
    });

    const unlocked_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "unlocked" },
    });

    res.status(200).json({
      message: "Employer profile plan fetched successfully.",
      data: convertBigIntToNumber({
        ...planDetails,
        emp_name: employer.emp_name,
        profile_picture: employer.profile_picture,
        creditsLeft: employerPlan.creditsLeft || 0,
        end_date: employerPlan.end_date || 0,
        candidate_counts: {
          shortlisted_count,
          unlocked_count,
        },
        // Include company data if available
        ...(company && {
          company_logo: company.company_logo,
          company_name: company.company_name,
          company_website: company.company_website,
        }),
      }),
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

// Update employer profile
exports.updateEmployerProfile = async (req, res) => {
  const emp_id = req.headers.authorization;
  const { emp_name, emp_mobile, designation, company_name, company_website } = req.body;

  if (!emp_id) {
    return res.status(401).json({
      message: "Authorization header (employer ID) is required.",
    });
  }

  try {
    // Validate required personal information
    if (!emp_name || !emp_mobile || !designation) {
      return res.status(400).json({
        message: "Full name, mobile number, and designation are required.",
      });
    }

    // Check if employer exists
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
    });

    if (!employer) {
      return res.status(404).json({
        message: "Employer not found.",
      });
    }

    // Prepare update data for employer (personal information)
    const employerUpdateData = {
      emp_name: emp_name.trim(),
      emp_mobile: BigInt(emp_mobile.toString().trim()),
      designation: designation.trim(),
    };

    // Handle profile picture upload if provided
    if (req.files?.profile_picture) {
      const profilePictureFile = req.files.profile_picture[0];
      const profilePicturePath = profilePictureFile.path.replace(/\\/g, "/");
      employerUpdateData.profile_picture = profilePicturePath;
    }

    // Update employer record with personal information
    await prisma.employer.update({
      where: { id: Number(emp_id) },
      data: employerUpdateData,
    });

    // Handle company information (optional, for backward compatibility)
    if (company_name || company_website || req.files?.company_logo) {
      const companyUpdateData = {};

      if (company_name && company_name.trim()) {
        companyUpdateData.company_name = company_name.trim();
      }

      if (company_website && company_website.trim()) {
        companyUpdateData.company_website = company_website.trim();
      }

      // Handle company logo upload if provided
      if (req.files?.company_logo) {
        const logoFile = req.files.company_logo[0];
        const logoPath = logoFile.path.replace(/\\/g, "/");
        companyUpdateData.company_logo = logoPath;

        // Synchronize: Update employer profile picture to match company logo
        await prisma.employer.update({
          where: { id: Number(emp_id) },
          data: { profile_picture: logoPath },
        });
      }

      // Update company record if company_id exists and there's data to update
      if (employer.company_id && Object.keys(companyUpdateData).length > 0) {
        await prisma.company.update({
          where: { id: employer.company_id },
          data: companyUpdateData,
        });
      } else if (Object.keys(companyUpdateData).length > 0) {
        // If no company exists but company data is provided, create one
        const newCompany = await prisma.company.create({
          data: {
            company_name: company_name ? company_name.trim() : emp_name.trim(),
            company_website: company_website ? company_website.trim() : "",
            ...companyUpdateData,
          },
        });

        // Link the new company to the employer
        await prisma.employer.update({
          where: { id: Number(emp_id) },
          data: { company_id: newCompany.id },
        });
      }
    }

    // Prepare response data
    const responseData = {
      emp_name: emp_name.trim(),
      emp_mobile: emp_mobile.toString().trim(),
      designation: designation.trim(),
    };

    if (req.files?.profile_picture) {
      responseData.profile_picture = req.files.profile_picture[0].path.replace(/\\/g, "/");
    }

    if (company_name) {
      responseData.company_name = company_name.trim();
    }

    if (company_website) {
      responseData.company_website = company_website.trim();
    }

    if (req.files?.company_logo) {
      responseData.company_logo = req.files.company_logo[0].path.replace(/\\/g, "/");
    }

    res.status(200).json({
      message: "Employer profile updated successfully.",
      data: responseData,
    });
  } catch (error) {
    console.error("Error updating employer profile:", error);
    return res.status(500).json({
      message: "Failed to update employer profile.",
      error: error.message
    });
  }
};

// Get employer and company details
exports.getEmployerDetails = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
    });

    if (!employer) {
      return res.status(404).json({
        message: "No employer details Found. Please log in again.",
      });
    }

    // Fetch company if company_id exists
    let company = null;
    if (employer.company_id !== null && employer.company_id !== undefined) {
      company = await prisma.company.findUnique({
        where: { id: employer.company_id },
      });
    }

    // Always return employer data, with company data if available
    const responseData = {
      ...employer,
      ...(company && company), // Merge company data if it exists
    };

    res.status(200).json({
      message: "Employer profile data fetched successfully.",
      data: convertBigIntToNumber(responseData),
    });
  } catch (error) {
    console.error("Error fetching Employer Data:", error);
    return res.status(500).json({ message: "Failed to fetch Employer Data." });
  }
};