import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { createPortal } from 'react-dom';

const Tooltip = ({ content, isOpen, onClose, targetRef, title }) => {
  const tooltipRef = useRef(null);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
  const [show, setShow] = useState(false);
  const positionReady = position.top !== 0 || position.left !== 0;

  // Set position synchronously before paint
  useLayoutEffect(() => {
    if (isOpen && targetRef.current) {
      const targetRect = targetRef.current.getBoundingClientRect();
      setPosition({
        top: targetRect.bottom + 8,
        left: targetRect.left,
        width: targetRect.width,
      });
    } else if (!isOpen) {
      setPosition({ top: 0, left: 0, width: 0 }); // Reset on close
    }
  }, [isOpen, targetRef, content]);

  // Only trigger animation after position is set and tooltip is mounted
  useEffect(() => {
    if (isOpen && positionReady) {
      setShow(false); // reset
      const timer = setTimeout(() => setShow(true), 10);
      return () => clearTimeout(timer);
    } else {
      setShow(false);
    }
  }, [isOpen, positionReady, position.top, position.left]);

  useEffect(() => {
    if (!isOpen) return;
    const handleClickOutside = (event) => {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target) &&
        targetRef.current &&
        !targetRef.current.contains(event.target)
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose, targetRef]);

  if (!isOpen || !positionReady) return null;

  return createPortal(
    <div
      ref={tooltipRef}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
        minWidth: position.width,
      }}
      className={`rounded-2xl border border-gray-200 bg-white dark:bg-gray-900 dark:border-gray-700 shadow-xl p-4 max-w-xs break-words transition-all duration-200 ease-out
        ${show ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'}
      `}
    >
      {title && (
        <div className="mb-2 text-base font-bold text-gray-900 dark:text-gray-100">{title}</div>
      )}
      <ul className="flex flex-col">
        {content.map((item, index) => (
          <React.Fragment key={index}>
            <li className="flex items-start gap-2 py-1">
              <span className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-indigo-400 dark:bg-indigo-300"></span>
              <span className="text-gray-900 dark:text-gray-100 text-sm whitespace-nowrap">{item}</span>
            </li>
            {index !== content.length - 1 && (
              <div className="w-full border-t border-gray-200 dark:border-gray-700 my-1" />
            )}
          </React.Fragment>
        ))}
      </ul>
    </div>,
    document.body
  );
};

export default Tooltip;
