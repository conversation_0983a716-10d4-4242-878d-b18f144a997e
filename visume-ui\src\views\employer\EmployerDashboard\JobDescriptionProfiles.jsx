import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useParams } from "react-router-dom";
import ProfileCard from "../ProfilesUI/ProfileCard";

const JobDescriptionProfiles = () => {
  const { id } = useParams();
  const [profiles, setProfiles] = useState([]);
  const [jd, setJD] = useState(null);
  const [loading, setLoading] = useState(true);


  useEffect(() => {
    async function fetchJDAndProfiles() {
      setLoading(true);
      try {
        // Fetch job description details
        const jdRes = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${id}`
        );
        let jdData = null;
        if (jdRes.ok) {
          const jdJson = await jdRes.json();
          // Support both jobDescription and jobDescriptions response formats
          jdData = jdJson.jobDescription || jdJson.jobDescriptions?.find(j => j._id === Number(id));
          // If jobDescription is nested, flatten it
          if (jdData && jdData.jobDescription) {
            jdData = { ...jdData, ...jdData.jobDescription };
          }
          setJD(jdData);
        }
        const emp_id =
          jdData?.emp_id ||
          (typeof Cookies !== "undefined"
            ? Cookies.get("employerId")
            : window.localStorage.getItem("employerId") || "");
        // Use filterCandidate endpoint for more accurate matching
        let filterUrl = `${import.meta.env.VITE_APP_HOST}/api/v1/filterCandidate?role=${encodeURIComponent(jdData?.role || "")}`;
        if (jdData?.skills && Array.isArray(jdData.skills) && jdData.skills.length > 0) {
          filterUrl += `&skills=${encodeURIComponent(jdData.skills.join(","))}`;
        } else if (jdData?.skills && typeof jdData.skills === "string" && jdData.skills.length > 0) {
          filterUrl += `&skills=${encodeURIComponent(jdData.skills)}`;
        }
        // Add cache-busting query param
        filterUrl += `&cb=${Date.now()}`;
        const profilesRes = await fetch(filterUrl, {
          headers: {
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Expires": "0"
          }
        });
        const profilesJson = await profilesRes.json();

        // If no JD found, show all candidate profiles
        let matchingProfiles;
        let candidatesList = profilesJson.candidateProfiles || profilesJson.candidates || [];
        if (!jdData) {
          matchingProfiles = candidatesList;
        } else {
          matchingProfiles = candidatesList.filter(profile => {
            const roleMatches =
              profile.role?.toLowerCase() === jdData?.role?.toLowerCase();

            // Normalize skills to arrays of trimmed, lowercase strings
            let profileSkills = [];
            if (Array.isArray(profile.skills)) {
              profileSkills = profile.skills.map(s => s.toLowerCase().trim());
            } else if (typeof profile.skills === "string") {
              profileSkills = profile.skills.toLowerCase().split(",").map(s => s.trim());
            }

            let jdSkills = [];
            if (Array.isArray(jdData?.skills)) {
              jdSkills = jdData.skills.map(s => s.toLowerCase().trim());
            } else if (typeof jdData?.skills === "string") {
              jdSkills = jdData.skills.toLowerCase().split(",").map(s => s.trim());
            }

            // Match if any skill overlaps, or if any skill is a substring of another
            const skillsMatch = jdSkills.some(skill =>
              profileSkills.some(ps =>
                ps.includes(skill) || skill.includes(ps)
              )
            );


            // Relaxed: match if role OR any skill matches (ignore location/experience for now)
            return roleMatches || skillsMatch;
          });
          // Fallback: if no matching profiles, show all candidates
          if (matchingProfiles.length === 0) {
            matchingProfiles = candidatesList;
          }
        }
        setProfiles(matchingProfiles);
      } catch (err) {
        setProfiles([]);
        console.error("Error fetching JD or profiles:", err);
      } finally {
        setLoading(false);
      }
    }
    if (id) fetchJDAndProfiles();
  }, [id]);

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Profiles for Job Description</h2>
      {jd && (
        <div className="mb-6 p-4 rounded bg-gray-100 dark:bg-navy-700">
          <div className="font-semibold text-lg">{jd.role}</div>
          <div className="text-sm">Location: {Array.isArray(jd.location) ? jd.location.join(", ") : jd.location}</div>
          <div className="text-sm">Experience: {jd.experience}</div>
          <div className="text-sm">Skills: {Array.isArray(jd.skills) ? jd.skills.join(", ") : jd.skills}</div>
        </div>
      )}
      {loading ? (
        <div>Loading profiles...</div>
      ) : profiles.length === 0 ? (
        <div>No matching profiles found.</div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {profiles.map((profile, idx) => (
            <ProfileCard
              key={profile.id || idx}
              {...profile}
              score={profile.score}
            />
          ))}
        </div>
      )}
      {/* If no JD, show all candidate profiles */}
      {!jd && !loading && profiles.length === 0 && (
        <div className="grid gap-4 md:grid-cols-2">
          {profiles.map((profile, idx) => (
            <ProfileCard
              key={profile.id || idx}
              {...profile}
              score={profile.score}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default JobDescriptionProfiles;