import React from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Home, HelpCircle, Mic, Video } from "lucide-react";

const InterviewFlowHeader = ({
  showBackButton = true,
  title = "Video Interview",
  currentStep = "",
  onBack = null,
  // Interview-specific props
  isInterviewSection = false,
  currentIndex = null,
  totalQuestions = null,
  showAudioStatus = false,
  showVideoStatus = false,
}) => {
  const navigate = useNavigate();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(-1);
    }
  };

  const handleHome = () => {
    navigate("/candidate/dashboard");
  };

  return (
    <header className="sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-b border-gray-200 dark:border-gray-800 shadow-sm">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className={`flex items-center justify-between ${isInterviewSection ? 'h-14' : 'h-12'}`}>
          {/* Left: Navigation */}
          <div className="flex items-center gap-2">
            {showBackButton && (
              <button
                onClick={handleBack}
                className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Go back"
              >
                <ArrowLeft className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </button>
            )}
            
            {!isInterviewSection && (
            <button
              onClick={handleHome}
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="Go to dashboard"
            >
              <Home className="h-4 w-4 text-gray-600 dark:text-gray-400" />
            </button>
            )}
          </div>

          {/* Center: Logo and Title */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 bg-gradient-to-br from-blue-600 to-purple-600 rounded-md flex items-center justify-center">
                <span className="text-white font-bold text-xs">V</span>
              </div>
              <span className="text-base font-semibold text-gray-900 dark:text-white">
                Visume
              </span>
            </div>
            
            {/* Section Title */}
            {title && (
              <div className="flex items-center gap-2">
                <div className="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
                <h1 className="text-base font-semibold text-gray-900 dark:text-white">
                  {title}
                </h1>
              </div>
            )}
          </div>

          {/* Right: Interview Status or Help */}
          <div className="flex items-center gap-2">
            {isInterviewSection ? (
              <>
                {/* Question Progress */}
                {currentIndex !== null && totalQuestions !== null && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                      {currentIndex + 1}/{totalQuestions}
                    </span>
                    
                    {/* Status Indicators */}
                    <div className="flex items-center gap-1.5">
                      {showAudioStatus && (
                        <div className="flex items-center gap-1 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md px-1.5 py-0.5">
                          <div className="relative">
                            <Mic className="w-2.5 h-2.5 text-green-600 dark:text-green-400" />
                            <div className="absolute -top-0.5 -right-0.5 h-1 w-1 bg-green-500 rounded-full animate-pulse"></div>
                          </div>
                          <span className="text-[10px] font-medium text-green-700 dark:text-green-300">
                            MIC
                          </span>
                        </div>
                      )}
                      {showVideoStatus && (
                        <div className="flex items-center gap-1 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md px-1.5 py-0.5">
                          <div className="relative">
                            <Video className="w-2.5 h-2.5 text-blue-600 dark:text-blue-400" />
                            <div className="absolute -top-0.5 -right-0.5 h-1 w-1 bg-blue-500 rounded-full animate-pulse"></div>
                          </div>
                          <span className="text-[10px] font-medium text-blue-700 dark:text-blue-300">
                            CAM
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <button
                className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Help & Support"
              >
                <HelpCircle className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default InterviewFlowHeader;