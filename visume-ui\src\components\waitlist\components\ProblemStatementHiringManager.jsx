const ProblemStatementHiringManager = () => {
  return (
    <section id="introduction" className="py-6 lg:py-8 bg-white relative">
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
        
        .card-grid {
          display: grid;
          gap: 1.25rem;
          margin-top: 1.5rem;
        }
        @media(min-width: 768px) {
          .card-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }
        
        .visume-card {
          background: #ffffff;
          border: 2px solid #dbeafe;
          border-radius: 2rem;
          padding: 0;
          display: flex;
          flex-direction: column;
          position: relative;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          aspect-ratio: 4/5;
          box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12);
        }
        
        .visume-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.04) 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 1;
        }
        
        .visume-card:hover::before {
          opacity: 1;
        }
        
        .visume-card:hover {
          transform: translateY(-12px);
          border-color: #3b82f6;
          box-shadow: 0 24px 48px rgba(59, 130, 246, 0.2);
        }
        
        /* Top Half - Animation Area */
        .animation-half {
          height: 60%;
          width: 100%;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          position: relative;
          overflow: hidden;
          border-radius: 1.8rem 1.8rem 0 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        /* Bottom Half - Text Area */
        .text-half {
          height: 40%;
          width: 100%;
          padding: 1rem 1.5rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          position: relative;
          z-index: 2;
          background: white;
        }
        
        /* Card 1 Visual - Resume Grid with Zoom Animation */
        .resume-grid-container {
          width: 100%;
          height: 100%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: scale(1.4);
        }

        .resume-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          grid-template-rows: repeat(3, 1fr);
          gap: 4px;
          width: 80px;
          height: 80px;
          animation: gridZoom 4s ease-in-out infinite;
        }

        .grid-resume {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
          border-radius: 3px;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          padding: 2px;
          box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
          animation: resumePulse 4s ease-in-out infinite;
        }

        .grid-resume::before {
          content: '';
          width: 80%;
          height: 2px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 1px;
          margin-bottom: 1px;
        }

        .grid-resume::after {
          content: '';
          width: 60%;
          height: 1px;
          background: rgba(255, 255, 255, 0.6);
          border-radius: 1px;
        }

        .grid-resume:nth-child(1) { animation-delay: 0s; }
        .grid-resume:nth-child(2) { animation-delay: 0.2s; }
        .grid-resume:nth-child(3) { animation-delay: 0.4s; }
        .grid-resume:nth-child(4) { animation-delay: 0.6s; }
        .grid-resume:nth-child(5) { animation-delay: 0.8s; }
        .grid-resume:nth-child(6) { animation-delay: 1s; }
        .grid-resume:nth-child(7) { animation-delay: 1.2s; }
        .grid-resume:nth-child(8) { animation-delay: 1.4s; }
        .grid-resume:nth-child(9) { animation-delay: 1.6s; }

        @keyframes gridZoom {
          0%, 100% { transform: scale(1); }
          25% { transform: scale(0.8); }
          50% { transform: scale(1.2); }
          75% { transform: scale(0.9); }
        }

        @keyframes resumePulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.05); }
        }
        
        /* Card 2 Visual - Animated pressure clock */
        .pressure-clock {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
          position: relative;
          transform: scale(1.4);
        }

        .clock-face {
          width: 80px;
          height: 80px;
          border: 5px solid #3b82f6;
          border-radius: 50%;
          position: relative;
          animation: clockPressure 3s ease-in-out infinite;
          background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
          box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .clock-hand-hour {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 3px;
          height: 20px;
          background: #1e40af;
          transform-origin: bottom;
          transform: translate(-50%, -100%) rotate(45deg);
          animation: hourHand 4s linear infinite;
          border-radius: 2px;
        }

        .clock-hand-minute {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 2px;
          height: 28px;
          background: #3b82f6;
          transform-origin: bottom;
          transform: translate(-50%, -100%) rotate(90deg);
          animation: minuteHand 2s linear infinite;
          border-radius: 1px;
        }

        .pressure-waves {
          position: absolute;
          width: 110px;
          height: 110px;
          border: 3px solid #3b82f6;
          border-radius: 50%;
          animation: pressureWave 2s ease-out infinite;
          opacity: 0;
        }

        .pressure-waves:nth-child(2) { animation-delay: 0.5s; }
        .pressure-waves:nth-child(3) { animation-delay: 1s; }

        @keyframes clockPressure {
          0%, 100% { transform: scale(1); border-color: #3b82f6; }
          50% { transform: scale(1.1); border-color: #1e40af; }
        }

        @keyframes hourHand {
          0% { transform: translate(-50%, -100%) rotate(45deg); }
          100% { transform: translate(-50%, -100%) rotate(405deg); }
        }

        @keyframes minuteHand {
          0% { transform: translate(-50%, -100%) rotate(90deg); }
          100% { transform: translate(-50%, -100%) rotate(450deg); }
        }

        @keyframes pressureWave {
          0% { transform: scale(0.8); opacity: 0.8; }
          100% { transform: scale(1.5); opacity: 0; }
        }
        
        /* Card 3 Visual - Animated cultural fit puzzle */
        .culture-puzzle {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          height: 100%;
          width: 100%;
          position: relative;
          padding: 20px;
          transform: scale(1.5);
        }

        .puzzle-piece {
          width: 28px;
          height: 28px;
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
          position: relative;
          animation: puzzleFloat 4s ease-in-out infinite;
          border-radius: 4px;
          box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
        }

        .puzzle-piece::before {
          content: '';
          position: absolute;
          top: -4px;
          left: 50%;
          transform: translateX(-50%);
          width: 8px;
          height: 8px;
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
          border-radius: 50%;
        }

        .puzzle-piece::after {
          content: '';
          position: absolute;
          right: -4px;
          top: 50%;
          transform: translateY(-50%);
          width: 8px;
          height: 8px;
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
          border-radius: 50%;
        }

        .puzzle-piece:nth-child(1) { animation-delay: 0s; }
        .puzzle-piece:nth-child(2) { animation-delay: 0.5s; }
        .puzzle-piece:nth-child(4) { animation-delay: 1.5s; }
        .puzzle-piece:nth-child(5) { animation-delay: 2s; }

        .missing-piece {
          width: 28px;
          height: 28px;
          border: 3px dashed #94a3b8;
          border-radius: 4px;
          position: relative;
          animation: missingPulse 3s ease-in-out infinite;
        }

        @keyframes puzzleFloat {
          0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.8; }
          50% { transform: translateY(-8px) rotate(5deg); opacity: 1; }
        }

        @keyframes missingPulse {
          0%, 100% { border-color: #94a3b8; transform: scale(1); }
          50% { border-color: #3b82f6; transform: scale(1.1); }
        }
        
        .blue-gradient-text {
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        .text-half h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.3;
          font-family: 'Manrope', sans-serif;
        }
        
        .text-half p {
          margin: 0;
          font-size: 0.8rem;
          color: #6b7280;
          line-height: 1.5;
          font-weight: 400;
          font-family: 'Sora', sans-serif;
        }
        
        @media (max-width: 768px) {
          .visume-card {
            aspect-ratio: 5/4;
          }
          .animation-half {
            height: 55%;
          }
          .text-half {
            height: 45%;
            padding: 1rem 1.25rem;
          }
          .text-half h3 {
            font-size: 1rem;
          }
          .text-half p {
            font-size: 0.8rem;
          }
        }
      `}</style>
      <div className="max-w-4xl mx-auto px-6">
        {/* Top badge - Updated to match other sections */}
        <div className="text-center mb-8">
          <div className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm">
            <span style={{ fontFamily: "Sora, sans-serif" }}>Why Visume?</span>
          </div>
        </div>
        {/* Main heading */}
        <h2
          className="text-center text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 tracking-tight leading-tight mb-3"
          style={{ fontFamily: "Manrope, sans-serif" }}
        >
          Hiring the right talent feels <span className="blue-gradient-text">overwhelming and risky</span>
        </h2>
        {/* Animated Cards */}
        <div className="card-grid">
          <div className="visume-card">
            <div className="animation-half">
              <div className="resume-grid-container">
                <div className="resume-grid">
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                  <div className="grid-resume"></div>
                </div>
              </div>
            </div>
            <div className="text-half">
              <h3>
                Drowning in <span className="blue-gradient-text">identical resumes</span>
              </h3>
              <p>Hundreds of similar-looking CVs make it impossible to identify standout candidates.</p>
            </div>
          </div>
          <div className="visume-card">
            <div className="animation-half">
              <div className="pressure-clock">
                <div className="pressure-waves"></div>
                <div className="pressure-waves"></div>
                <div className="pressure-waves"></div>
                <div className="clock-face">
                  <div className="clock-hand-hour"></div>
                  <div className="clock-hand-minute"></div>
                </div>
              </div>
            </div>
            <div className="text-half">
              <h3>
                Time pressure leads to <span className="blue-gradient-text">poor decisions</span>
              </h3>
              <p>Rushed hiring processes result in costly mis-hires and team disruption.</p>
            </div>
          </div>
          <div className="visume-card">
            <div className="animation-half">
              <div className="culture-puzzle">
                <div className="puzzle-piece"></div>
                <div className="puzzle-piece"></div>
                <div className="missing-piece"></div>
                <div className="puzzle-piece"></div>
                <div className="puzzle-piece"></div>
              </div>
            </div>
            <div className="text-half">
              <h3>
                Culture fit is <span className="blue-gradient-text">impossible to assess</span>
              </h3>
              <p>You can't tell from a resume if someone will thrive in your team environment.</p>
            </div>
          </div>
        </div>
        {/* Final statement */}
        <div className="mt-12 text-center">
          <p
            className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 tracking-tight leading-tight max-w-5xl mx-auto"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            We're giving you <span className="blue-gradient-text">deeper candidate insights</span> — helping you make
            confident hiring decisions and build teams with{" "}
            <span className="blue-gradient-text">perfect cultural alignment</span>.
          </p>
        </div>
      </div>
    </section>
  )
}

export default ProblemStatementHiringManager
