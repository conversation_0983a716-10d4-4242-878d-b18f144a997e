import { useState, useCallback, useEffect, useRef } from "react";

export function useRecording(localCamStream) {
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [recordedVideoUrl, setRecordedVideoUrl] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const chunks = useRef([]);
  
  // Audio mixing refs
  const audioContext = useRef(null);
  const mixedStream = useRef(null);
  const pollyAudioSource = useRef(null);
  const micAudioSource = useRef(null);
  const gainNode = useRef(null);
  const destination = useRef(null);

  // Effect to set up media recorder with mixed audio
  useEffect(() => {
    if (!localCamStream) return;

    const setupMixedStream = async () => {
      try {
        // Create audio context for mixing
        audioContext.current = new (window.AudioContext || window.webkitAudioContext)();
        
        // Create destination for mixed audio
        destination.current = audioContext.current.createMediaStreamDestination();
        
        // Get audio track from webcam stream
        const audioTracks = localCamStream.getAudioTracks();
        if (audioTracks.length > 0) {
          // Create source from microphone audio
          const micStream = new MediaStream([audioTracks[0]]);
          micAudioSource.current = audioContext.current.createMediaStreamSource(micStream);
          
          // Create gain node for volume control
          gainNode.current = audioContext.current.createGain();
          gainNode.current.gain.value = 1.0;
          
          // Connect microphone to mixed output
          micAudioSource.current.connect(gainNode.current);
          gainNode.current.connect(destination.current);
        }
        
        // Create mixed stream with video from webcam and mixed audio
        const videoTracks = localCamStream.getVideoTracks();
        const mixedAudioTracks = destination.current.stream.getAudioTracks();
        
        mixedStream.current = new MediaStream([
          ...videoTracks,
          ...mixedAudioTracks
        ]);
        
        // Create recorder with mixed stream
        const recorder = new MediaRecorder(mixedStream.current, {
          mimeType: "video/webm;codecs=vp8,opus",
          videoBitsPerSecond: 5000000, // 5 Mbps for better quality
          audioBitsPerSecond: 128000   // 128 kbps for audio
        });
        
        setupRecorderEvents(recorder);
        setMediaRecorder(recorder);
        
      } catch (error) {
        console.error("Error setting up mixed audio recording:", error);
        // Fallback to original stream if mixing fails
        const recorder = new MediaRecorder(localCamStream, {
          mimeType: "video/webm;codecs=vp8,opus",
          videoBitsPerSecond: 5000000,
          audioBitsPerSecond: 128000
        });
        setupRecorderEvents(recorder);
        setMediaRecorder(recorder);
      }
    };

    const setupRecorderEvents = (recorder) => {
      recorder.onstart = () => {
        console.log("Recording started");
        chunks.current = [];
        setIsRecording(true);
        setRecordedVideoUrl(null);
      };

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.current.push(event.data);
          // Preserve previous chunks and add new one
          setRecordedChunks(currentChunks => {
            const newChunks = [...currentChunks, event.data];
            // Create and update blob URL whenever we get new data
            const blob = new Blob(newChunks, { type: "video/webm" });
            try {
              if (recordedVideoUrl) {
                URL.revokeObjectURL(recordedVideoUrl);
              }
              const url = URL.createObjectURL(blob);
              setRecordedVideoUrl(url);
            } catch (error) {
              console.error("Error updating video URL:", error);
            }
            return newChunks;
          });
        }
      };

      recorder.onstop = () => {
        console.log("Recording stopped");
        setIsRecording(false);
        // Final processing is already done in ondataavailable
      };
    };

    setupMixedStream();

    return () => {
      if (audioContext.current && audioContext.current.state !== 'closed') {
        audioContext.current.close();
      }
      if (mixedStream.current) {
        mixedStream.current.getTracks().forEach(track => track.stop());
      }
      if (recordedVideoUrl) {
        URL.revokeObjectURL(recordedVideoUrl);
      }
      chunks.current = [];
    };
  }, [localCamStream]);

  // Function to add Polly audio to the mix
  const addPollyAudioToMix = useCallback((audioElement) => {
    if (!audioContext.current || !destination.current) return;
    
    try {
      // Remove previous Polly source if exists
      if (pollyAudioSource.current) {
        pollyAudioSource.current.disconnect();
      }
      
      // Create source from Polly audio element
      pollyAudioSource.current = audioContext.current.createMediaElementSource(audioElement);
      
      // Connect Polly audio to mixed output
      pollyAudioSource.current.connect(destination.current);
      
      // Also connect to default output so user can hear it
      pollyAudioSource.current.connect(audioContext.current.destination);
      
    } catch (error) {
      console.error("Error adding Polly audio to mix:", error);
    }
  }, []);

  const startRecording = useCallback(async () => {
    if (!mediaRecorder || isRecording) return;
    
    try {
      // Clear previous recording if exists
      if (recordedVideoUrl) {
        URL.revokeObjectURL(recordedVideoUrl);
        setRecordedVideoUrl(null);
      }
      chunks.current = [];
      setRecordedChunks([]);
      
      await new Promise((resolve) => {
        mediaRecorder.onstart = () => {
          console.log("Recording started successfully");
          resolve();
        };
        mediaRecorder.start(500); // Collect data every 500ms for smoother recording
      });
      
      return true;
    } catch (error) {
      console.error("Failed to start recording:", error);
      return false;
    }
  }, [mediaRecorder, isRecording, recordedVideoUrl]);

  const stopRecording = useCallback(async () => {
    if (!mediaRecorder || !isRecording) return;
    
    try {
      await new Promise((resolve) => {
        mediaRecorder.onstop = () => {
          console.log("Recording stopped successfully");
          resolve();
        };
        mediaRecorder.stop();
      });
      
      return true;
    } catch (error) {
      console.error("Failed to stop recording:", error);
      return false;
    }
  }, [mediaRecorder, isRecording]);

  const handleDownload = useCallback(() => {
    if (recordedVideoUrl) {
      const a = document.createElement("a");
      a.style = "display: none";
      a.href = recordedVideoUrl;
      a.download = "interview-recording.webm";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }, [recordedVideoUrl]);

  return {
    startRecording,
    stopRecording,
    handleDownload,
    isRecording,
    recordedVideoUrl,
    recordedChunks,
    addPollyAudioToMix
  };
}
