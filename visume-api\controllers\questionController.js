const { generateSingleQuestion } = require("../utils/helpers");

exports.generateNextQuestion = async (req, res) => {

  try {
    const { role, skills, previousQuestions, companyType, experience, videoProfileId, completeResumeData } = req.body;

    // Validate required fields
    if (!role) {
      return res.status(400).json({
        success: false,
        message: "Role is required for question generation"
      });
    }

    // Parse and validate previous questions array
    // Frontend sends previousQuestions as an array of objects, each containing question and answer
    const previousQA = (previousQuestions || []).map((q) => ({
      question: q.question,
      answer: q.answer || null, // Directly use the answer from the question object
      type: q.type,
      question_number: q.question_number,
      total_questions: q.total_questions
    }));


    // Use standard interview length without requirement-based calculation
    let totalQuestions = 10; // Standard maximum interview length

    const questionCount = (previousQuestions || []).length + 1;

    // Phase 3: Simple count-based interview termination - interview ends after exactly N questions
    if (questionCount > totalQuestions) {
      return res.status(200).json({
        success: false,
        message: "Interview completed",
        completed: true,
        totalQuestionsCompleted: questionCount - 1,
        maxQuestions: totalQuestions
      });
    }


    // Define isFirstQuestion early for use in real-time analysis
    const isFirstQuestion = !previousQuestions || previousQuestions.length === 0;

    // Real-time requirement analysis removed - no longer using AI-driven requirement assessment

    // Question type determination using simple alternation
    // Remove forcedType logic; let AI decide question type
    let forcedType = null;

    const projects = completeResumeData?.projects || [];
    const nextQuestion = await generateSingleQuestion(
      role,
      previousQA,
      skills,
      isFirstQuestion,
      companyType,
      experience,
      null, // Let AI decide type
      questionCount, // Pass current question number for AI termination logic
      projects // Pass candidate projects for prompt context
    );

    // Check if AI determined interview should be terminated
    if (nextQuestion.type === 'ai_interview_stop') {
      return res.status(200).json({
        success: false,
        message: "Interview completed by AI assessment",
        completed: true,
        aiTerminated: true,
        totalQuestionsCompleted: questionCount - 1,
        maxQuestions: totalQuestions,
        terminationReason: "AI determined sufficient information gathered"
      });
    }

    // Validate and ensure timerDuration is within acceptable range
    const timerDuration = Math.min(90, Math.max(30, parseInt(nextQuestion.timerDuration) || 90));

    const response = {
      success: true,
      question: nextQuestion.question,
      type: nextQuestion.type,
      timerDuration: timerDuration,
      question_number: questionCount,
      total_questions: totalQuestions,
      completed: false,
      aiTerminated: false,
      _fallback: nextQuestion._fallback || false
    };

    res.status(200).json(response);

  } catch (error) {
    
    // Simple fallback
    const fallbackQuestion = {
      // Remove fallback questions; throw error if no questions can be generated
      question: null,
      type: "behavioral",
      timerDuration: 90, // Default fallback duration
      _fallback: true
    };

    const status = error.message === "Interview completed" ? 200 : 500;
    res.status(status).json({
      success: status === 200,
      ...fallbackQuestion,
      completed: error.message === "Interview completed",
      error_details: error.message
    });
  }
};
