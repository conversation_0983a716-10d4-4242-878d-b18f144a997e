# Visume API

Visume API is the backend service for the Visume platform, powering AI-driven video resume creation, candidate evaluation, and intelligent job-candidate matching.

---

## Table of Contents

- [About](#about)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Environment Variables](#environment-variables)
  - [Database Setup](#database-setup)
  - [Running the Server](#running-the-server)
  - [Seeding the Database](#seeding-the-database)
- [API Overview](#api-overview)
- [Contributing](#contributing)
- [License](#license)

---

## About

Visume API enables:

- **Candidates** to create video resumes by providing role, skills, company size, and experience, then participate in AI-driven interviews. The backend evaluates responses and returns detailed scores and feedback.
- **Employers** to create job descriptions. The backend matches and returns relevant candidate profiles, allowing employers to shortlist and manage candidates efficiently.

---

## Features

- RESTful API for candidate and employer operations
- AI-powered interview evaluation and scoring
- Job description creation and candidate-job matching
- JWT-based authentication and authorization
- File uploads for resumes and profile pictures
- Prisma ORM for database management

---

## Tech Stack

- **Node.js** (runtime)
- **Express** (web framework)
- **Prisma** (ORM)
- **PostgreSQL** (database)
- **JWT** (authentication)
- **Multer** (file uploads)

---

## Project Structure

```
visume-api/
├── config/                 # Configuration files
├── controllers/            # Route controllers
├── middlewares/            # Express middlewares
├── prisma/                 # Prisma schema and migrations
├── routes/                 # API route definitions
├── uploads/                # Uploaded files
├── utils/                  # Utility functions
├── .env.example            # Example environment variables
├── package.json            # Project metadata and scripts
└── README.MD               # Project documentation
```

---

## Getting Started

### Prerequisites

- Node.js (v16 or above)
- npm (v8 or above)
- PostgreSQL (local or remote)

### Installation

Clone the repository and install dependencies:

```bash
git clone https://github.com/your-org/visume-api.git
cd visume-api
npm install
```

### Environment Variables

Copy `.env.example` to `.env` and update the values as needed:

```bash
cp .env.example .env
```

- `PORT`: Port to run the server (default: 5000)
- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret for JWT authentication
- `GOOGLE_CLIENT_ID` / `GOOGLE_CLIENT_SECRET`: For Google OAuth (if used)

### Database Setup

Run Prisma migrations to set up the database schema:

```bash
npx prisma migrate deploy
```

### Running the Server

Start the backend server:

```bash
npm run start:dev
```

The API will be available at [http://localhost:5000](http://localhost:5000).

### Seeding the Database

(Optional) Seed the database with initial data:

```bash
node prisma/seed.js
```

---

## API Overview

- All endpoints are defined in the [`routes/`](routes/:1) directory.
- Authentication is required for protected routes (JWT).
- File uploads are handled for resumes and profile pictures.
- See the codebase for detailed endpoint documentation.

---

## Contributing

1. Fork the repository
2. Create a new branch (`git checkout -b feature/your-feature`)
3. Commit your changes
4. Push to your fork and submit a pull request

---

## License

This project is licensed. See the main repository for details.