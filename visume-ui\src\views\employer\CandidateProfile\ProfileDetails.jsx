// ProfileDetails.jsx
import React from "react";
import { Mail, Phone } from "lucide-react";

const ProfileDetails = ({
  candidateProf,
  strippedResumeJson,
  detailsBlur,
  setSubPopup,
  questionsAndAnswers,
}) => {
  return (
  <div>
    {/* Skills */}
    <section className="rounded-2xl bg-white p-4">
      <div className="mb-3 flex items-center justify-between">
        <h2 className="text-xl font-semibold">Skills</h2>
      </div>
      <div className="flex flex-wrap gap-2">
        {strippedResumeJson &&
          Array.isArray(strippedResumeJson.skills) &&
          strippedResumeJson.skills.map((skill, index) => (
            <span
              key={index}
              className="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800"
            >
              {skill.trim()}
            </span>
          ))}
      </div>
    </section>

    {/* Experience Section */}
    <div className="space-y-4 rounded-xl bg-white  p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">
          All Experience
        </h2>
      </div>
      <div className="space-y-5">
        {strippedResumeJson &&
          strippedResumeJson.workExperience.map((experience, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-start space-x-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-brand-700 object-cover text-2xl text-white shadow-sm">
                  {experience.company[0]}
                </div>
                <div className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-lg font-semibold text-gray-800">
                      {experience.company}
                    </h3>
                    <span className="text-sm text-gray-500">
                      {experience.dates}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-brand-600">
                    {experience.title}
                  </p>
                  <ul className="ml-5 list-disc space-y-1 text-sm text-gray-600">
                    {experience.responsibilities.map(
                      (responsibility, idx) => (
                        <li key={idx}>{responsibility}</li>
                      )
                    )}
                  </ul>
                </div>
              </div>
              {strippedResumeJson &&
                index <
                  strippedResumeJson.workExperience.length - 1 && (
                  <hr className="mt-3 border-t border-gray-200" />
                )}
            </div>
          ))}
      </div>
    </div>

    {/* Education Section */}
    <div className="space-y-4 rounded-2xl bg-white p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Education</h2>
      </div>
      <div className="space-y-3">
        {strippedResumeJson &&
          strippedResumeJson.education.map((edu, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-brand-700 object-cover text-2xl text-white shadow-sm">
                  {edu.institution[0]}
                </div>
                <div className="flex flex-col ">
                  <div className="flex items-start space-x-2">
                    <h3 className="text-md font-semibold">
                      {edu.institution}
                    </h3>
                    <span className="rounded-full border border-blue-500 px-2 py-0.5 text-sm text-blue-500">
                      Full-Time
                    </span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-800">{edu.degree}</p>
                    <p className="text-sm text-gray-400">
                      {edu.dates} - {edu.location || edu.coursework}
                    </p>
                  </div>
                </div>
              </div>
              {strippedResumeJson &&
                index < strippedResumeJson.education.length - 1 && (
                  <hr className="mt-3 rounded-full border border-gray-200" />
                )}
            </div>
          ))}
      </div>
    </div>

    {/* All Personal Information */}
    <div className="space-y-4 rounded-2xl bg-white p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">All Personal Information</h2>
      </div>
      <div className="relative">
        <div
          className={`flex flex-col space-y-4 md:flex-row md:justify-between md:space-y-0 ${
            detailsBlur ? "blur-md" : ""
          }`}
        >
          <div className="flex flex-col space-y-3 md:space-y-3">
            <div className="flex items-center space-x-2">
              <Mail className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
              <p className="text-sm">{candidateProf.cand_email}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
              <p className="text-sm">{candidateProf.cand_mobile}</p>
            </div>
          </div>
        </div>
        {detailsBlur && (
          <button
            onClick={() => setSubPopup(true)}
            className="absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 transform items-center space-x-2 rounded-full bg-brand-600 px-4 py-2 font-bold text-white transition duration-300 ease-in-out hover:bg-brand-700"
            aria-label="Unlock candidate details"
          >
            <span>Unlock Details</span>
          </button>
        )}
      </div>
    </div>

    {/* Projects */}
    <div className="space-y-4 rounded-xl bg-white p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">Projects</h2>
      </div>
      <div className="space-y-5">
        {strippedResumeJson &&
          Array.isArray(strippedResumeJson.projects) &&
          strippedResumeJson.projects.map((project, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-start space-x-4">
                <div className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-lg font-semibold text-gray-800">
                      {project.name}
                    </h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    {project.description}
                  </p>
                  <div className="flex flex-wrap space-x-2">
                    {Array.isArray(project.technologies) &&
                      project.technologies.map((tech, idx) => (
                        <span
                          key={idx}
                          className="rounded-lg bg-brand-500 px-2 py-1 text-xs font-thin text-white"
                        >
                          {tech}
                        </span>
                      ))}
                  </div>
                </div>
              </div>
              {index < strippedResumeJson.projects.length - 1 && (
                <hr className="mt-3 border-t border-gray-200" />
              )}
            </div>
          ))}
      </div>
    </div>

    {/* Languages */}
    <section className="rounded-2xl bg-white p-6 ">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Languages</h2>
      </div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
        {candidateProf.languages_known
          .split(",")
          .map((lang) => (
            <div
              key={lang.slice(0, 2).toUpperCase()}
              className="flex items-center space-x-3 rounded-lg border border-b-gray-200 bg-white p-2"
            >
              <div className="flex h-9 w-9 items-center justify-center rounded-full bg-brand-600 p-2  text-white">
                <span className="text-md font-semibold">
                  {lang.slice(0, 2).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="font-sm ">{lang}</p>
              </div>
            </div>
          ))}
      </div>
    </section>
  </div>
  );
};

export default ProfileDetails;