/**
 * VideoUploadService - Unified service for handling video uploads
 * 
 * Automatically chooses between single upload and multipart upload based on file size
 * Preserves existing functionality while adding multipart capabilities
 */

import { MultipartVideoUpload, shouldUseMultipartUpload } from './multipartUpload.js';

// File size threshold for multipart upload (10MB)
const MULTIPART_THRESHOLD = 10 * 1024 * 1024;

/**
 * Upload video using the appropriate method based on file size
 * 
 * @param {File} file - The video file to upload
 * @param {string} vpid - Video profile ID (used as filename)
 * @param {Object} options - Upload options
 * @param {Function} options.onProgress - Progress callback
 * @param {Function} options.onError - Error callback
 * @param {Function} options.setLoadingText - Loading text setter
 * @returns {Promise<string>} - Final video URL
 */
export async function uploadVideo(file, vpid, options = {}) {
  const {
    onProgress = () => {},
    onError = () => {},
    setLoadingText = () => {}
  } = options;

  console.log('VideoUploadService: Starting upload', {
    fileSize: file.size,
    fileName: vpid,
    useMultipart: shouldUseMultipartUpload(file.size, MULTIPART_THRESHOLD)
  });

  // Determine upload method based on file size
  if (shouldUseMultipartUpload(file.size, MULTIPART_THRESHOLD)) {
    return uploadVideoMultipart(file, vpid, { onProgress, onError, setLoadingText });
  } else {
    return uploadVideoSingle(file, vpid, { onProgress, onError, setLoadingText });
  }
}

/**
 * Upload video using multipart upload for large files
 */
async function uploadVideoMultipart(file, vpid, { onProgress, onError, setLoadingText }) {
  console.log('Using multipart upload for large file');
  
  setLoadingText("Preparing multipart upload...");
  
  const uploader = new MultipartVideoUpload(file, {
    fileName: vpid,
    chunkSize: 10 * 1024 * 1024, // 10MB chunks
    maxConcurrency: 3,
    onProgress: (progressData) => {
      setLoadingText(`Uploading Video: ${Math.round(progressData.percentage)}% (${progressData.completedParts}/${progressData.totalParts} parts)`);
      onProgress(progressData);
    },
    onError: (error) => {
      console.error('Multipart upload error:', error);
      onError(error);
    },
    onPartComplete: (partNumber, partInfo) => {
      console.log(`Part ${partNumber} completed:`, partInfo);
    }
  });

  try {
    const finalUrl = await uploader.start();
    setLoadingText("Upload completed successfully!");
    return finalUrl;
  } catch (error) {
    setLoadingText("Upload failed");
    throw error;
  }
}

/**
 * Upload video using single upload for smaller files (existing logic)
 */
async function uploadVideoSingle(file, vpid, { onProgress, onError, setLoadingText }) {
  console.log('Using single upload for small file');
  
  setLoadingText("Uploading Video...");
  
  const maxRetries = 3;
  let lastError = null;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Get a fresh pre-signed URL for each attempt
      console.log(`Getting pre-signed URL (attempt ${attempt + 1}/${maxRetries})`);
      const host = import.meta.env.VITE_APP_HOST || "https://api.zoomjobs.in";
      
      const urlResponse = await fetch(
        `${host}/api/v1/get-s3-url/${encodeURIComponent(vpid)}?contentType=${encodeURIComponent(file.type)}`,
        {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!urlResponse.ok) {
        const errorText = await urlResponse.text();
        throw new Error(`Failed to get upload URL: ${urlResponse.status} ${urlResponse.statusText}. Details: ${errorText}`);
      }

      const { url } = await urlResponse.json();
      console.log(`Got pre-signed URL (attempt ${attempt + 1})`);

      // Attempt the upload with the fresh URL
      console.log(`Uploading file (attempt ${attempt + 1}/${maxRetries})`);
      
      const uploadResponse = await fetch(url, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
          "Content-Length": file.size.toString(),
        },
        // Increase timeout for large files
        timeout: 300000, // 5 minutes
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}. Response: ${responseText}`);
      }

      // Upload successful
      console.log(`Single upload successful on attempt ${attempt + 1}`);
      const finalUrl = url.split("?")[0];
      
      // Simulate progress for consistency
      onProgress({
        percentage: 100,
        uploadedBytes: file.size,
        totalBytes: file.size,
        completedParts: 1,
        totalParts: 1
      });
      
      setLoadingText("Upload completed successfully!");
      return finalUrl;

    } catch (error) {
      console.error(`Single upload attempt ${attempt + 1} failed:`, error);
      lastError = error;

      if (attempt < maxRetries - 1) {
        const delay = 1000 * Math.pow(2, attempt); // Exponential backoff
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // All attempts failed
  setLoadingText("Upload failed after multiple attempts");
  onError(lastError);
  throw lastError;
}

/**
 * Get upload method info for debugging
 */
export function getUploadMethodInfo(fileSize) {
  const useMultipart = shouldUseMultipartUpload(fileSize, MULTIPART_THRESHOLD);
  return {
    method: useMultipart ? 'multipart' : 'single',
    fileSize,
    threshold: MULTIPART_THRESHOLD,
    fileSizeMB: Math.round(fileSize / (1024 * 1024) * 100) / 100,
    thresholdMB: Math.round(MULTIPART_THRESHOLD / (1024 * 1024))
  };
}

/**
 * Validate video file before upload
 */
export function validateVideoFile(file) {
  const errors = [];
  
  if (!file) {
    errors.push('No file provided');
    return { valid: false, errors };
  }
  
  if (file.size === 0) {
    errors.push('File is empty');
  }
  
  if (!file.type || !file.type.startsWith('video/')) {
    errors.push('File must be a video');
  }
  
  // Check for reasonable file size limits (e.g., 2GB max)
  const maxSize = 2 * 1024 * 1024 * 1024; // 2GB
  if (file.size > maxSize) {
    errors.push(`File too large. Maximum size is ${Math.round(maxSize / (1024 * 1024 * 1024))}GB`);
  }
  
  return {
    valid: errors.length === 0,
    errors,
    info: getUploadMethodInfo(file.size)
  };
}

export default {
  uploadVideo,
  getUploadMethodInfo,
  validateVideoFile,
  MULTIPART_THRESHOLD
};
