import React from "react";
import { motion } from "framer-motion";
const Footer = () => {
  return (
    <footer className="bg-[#1a1a1a] text-white">
      <div className="mx-auto max-w-7xl px-6 py-16">
        {/* Main Content */}
        <div className="mb-16 flex flex-col items-start justify-between lg:flex-row lg:items-center">
          {/* Left Side - Main Message */}
          <motion.div
            className="mb-8 max-w-lg lg:mb-0"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-light leading-tight text-gray-100 lg:text-4xl">
              Helping companies like yours exceed hiring goals.
            </h2>
          </motion.div>

          {/* Right Side - CTA Links */}
          <motion.div
            className="flex flex-col space-y-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <motion.a
              href="#"
              className="text-lg text-gray-300 transition-colors duration-200 hover:text-white"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              Get a free demo
            </motion.a>
            <motion.a
              href="#"
              className="text-lg text-gray-300 transition-colors duration-200 hover:text-white"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              Get in touch
            </motion.a>
          </motion.div>
        </div>

        {/* Divider Line */}
        <motion.div
          className="mb-8 border-t border-gray-700"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.8 }}
        />

        {/* Bottom Section */}
        <motion.div
          className="flex flex-col items-center justify-between md:flex-row"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          {/* Logo and Company */}
          <div className="mb-4 flex items-center space-x-4 md:mb-0">
            <motion.div
              className="flex items-center space-x-2"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="bg-transparent flex h-14 w-14 items-center justify-center rounded">
                <img
                  src="/visume-logo.png"
                  alt="Visume Logo"
                  className="h-12 w-12 object-contain"
                  style={{ display: "block" }}
                />
              </div>
              <span className="text-2xl font-bold text-white">Visume 2025</span>
            </motion.div>
          </div>

          {/* Tagline */}
          <p className="text-sm text-gray-400">A modern hiring company</p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
