import { parse } from "postcss";
import React, { useRef, useEffect, useState } from "react";

export default function VideoPreview({ stream, videoRef: externalVideoRef }) {
  const [name, setName] = useState("Default User");
  const [role, setRole] = useState("Node.js Developer");
  const [image, setImage] = useState(
    "https://cdn8.dissolve.com/p/D18_249_012/D18_249_012_0004_600.jpg"
  );
  const internalRef = useRef(null);
  const videoRef = externalVideoRef || internalRef;

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
    }
  }, [stream, videoRef]);

  useEffect(() => {
    const data = localStorage.getItem("formData");
    if (data) {
      const parsedData = JSON.parse(data);
      setRole(parsedData.jobRole);
      const url = `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${
        parsedData.candId
      }`;
      console.log(parsedData);
      // Make an API call using fetch
      fetch(url)
        .then((res) => res.json())
        .then((data) => {
          setName(data.candidateProfile[0].cand_name);
          if (data.candidateProfile[0].profile_picture) {
            const pic = data.candidateProfile[0].profile_picture;
            if (pic.startsWith("http")) {
              setImage(pic);
            } else {
              setImage(
                `${import.meta.env.VITE_APP_HOST}/${pic}`
              );
            }
          }
        })
        .catch((err) => console.log(err));
    }
  }, []);
  return (
    <div className="relative w-full h-full overflow-hidden rounded-xl bg-gray-900 shadow-lg">
      <video
        ref={videoRef}
        className="h-full w-full object-cover"
        autoPlay
        muted
      />
      
      {/* Enhanced overlay with gradient and modern styling */}
      <div className="absolute inset-0 pointer-events-none" />
      
      {/* User info overlay */}
      <div className="absolute bottom-0 left-0 right-0 p-6">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <img
              src={image}
              alt="User"
              className="h-14 w-14 rounded-full object-cover border-3 border-white/20 shadow-lg"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
            <div
              className="hidden h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 items-center justify-center text-white font-semibold text-lg border-3 border-white/20 shadow-lg"
            >
              {name ? name[0]?.toUpperCase() : 'U'}
            </div>
            {/* Online status indicator */}
            <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-green-500 rounded-full border-2 border-white"></div>
          </div>
          <div className="flex-1">
            <div className="text-xl font-semibold text-white mb-1">{name}</div>
            <div className="text-sm text-white/80 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1 inline-block">
              {role}
            </div>
          </div>
        </div>
      </div>

      {/* Recording indicator */}
      <div className="absolute top-4 right-4 flex items-center gap-2 bg-red-500/90 backdrop-blur-sm rounded-full px-3 py-1.5">
        <div className="h-2 w-2 bg-white rounded-full animate-pulse"></div>
        <span className="text-white text-xs font-medium">LIVE</span>
      </div>

      {/* Modern corner accents */}
      <div className="absolute top-0 left-0 w-8 h-8 border-l-2 border-t-2 border-white/20 rounded-tl-xl"></div>
      <div className="absolute top-0 right-0 w-8 h-8 border-r-2 border-t-2 border-white/20 rounded-tr-xl"></div>
      <div className="absolute bottom-0 left-0 w-8 h-8 border-l-2 border-b-2 border-white/20 rounded-bl-xl"></div>
      <div className="absolute bottom-0 right-0 w-8 h-8 border-r-2 border-b-2 border-white/20 rounded-br-xl"></div>
    </div>
  );
}
