const jobController = {
  getSuggestedJobs: async (req, res) => {
    try {
      // For now, returning mock data. In production, this would fetch from a database
      const suggestedJobs = [
        {
          title: "Senior Software Engineer",
          company: "TechCorp",
          openings: 3,
          url: "https://example.com/jobs/swe",
          image: "https://ui-avatars.com/api/?name=Tech+Corp&background=0D8ABC&color=fff"
        },
        {
          title: "Full Stack Developer",
          company: "WebSolutions",
          openings: 2,
          url: "https://example.com/jobs/fullstack",
          image: "https://ui-avatars.com/api/?name=Web+Solutions&background=FF6B6B&color=fff"
        },
        {
          title: "DevOps Engineer",
          company: "CloudTech",
          openings: 1,
          url: "https://example.com/jobs/devops",
          image: "https://ui-avatars.com/api/?name=Cloud+Tech&background=4CAF50&color=fff"
        }
      ];
      return res.status(200).json(suggestedJobs);
    } catch (error) {
      console.error("Error in getSuggestedJobs:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  }
};

module.exports = jobController;