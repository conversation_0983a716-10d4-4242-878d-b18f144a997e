import { defineConfig, loadEnv } from "vite";
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig(({ mode }) => {
    
    const env = loadEnv(mode, process.cwd());

    return {
        base: "/",
        plugins: [react()],
        define: {
            'process.env': {}, 
        },
        server: {
            
            proxy: {
                '^/api/.*': {
                    target: env.VITE_APP_HOST || 'https://api.zoomjobs.in',
                    changeOrigin: true,
                    secure: false,
                    configure: (proxy, _options) => {
                        proxy.on('error', (err, _req, _res) => {
                            console.log('proxy error', err);
                        });
                        proxy.on('proxyReq', (proxyReq, req, _res) => {
                            console.log('Sending Request:', req.method, req.url);
                            console.log('Target URL:', proxyReq.path);
                        });
                        proxy.on('proxyRes', (proxyRes, req, _res) => {
                            console.log('Received Response:', {
                                statusCode: proxyRes.statusCode,
                                url: req.url,
                                path: req.path
                            });
                        });
                    }
                }
            }
        },
        resolve: {
            alias: {
                '~': path.resolve(__dirname, './src'),
                'layouts': path.resolve(__dirname, './src/layouts'),
                'views': path.resolve(__dirname, './src/views'),
                'components': path.resolve(__dirname, './src/components'),
                'assets': path.resolve(__dirname, './src/assets'),
                'routes': path.resolve(__dirname, './src/routes'),
                'services': path.resolve(__dirname, './src/services')
            },
        },
    };
});
