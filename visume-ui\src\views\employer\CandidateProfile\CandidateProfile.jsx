// CandidateProfile.jsx (refactored)
import { ScoreCard } from "./ProfileHeader";
import { Star, MessageCircle, BarChart2 } from "lucide-react";
import React, { useEffect, useState, useRef } from "react";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useNavigate, useParams } from "react-router-dom";
import Loader from "components/Loader";
import CustomNavbar from "../components/CustomNavbar";
import {
  HiCollection,
  HiHome,
  HiOutlineSparkles,
  HiSearch,
  HiAcademicCap,
  HiBriefcase,
  HiBadgeCheck,
  HiStar,
  HiOutlineUser,
  HiOutlineGlobeAlt,
  HiOutlineDocumentText,
} from "react-icons/hi";
import { MdBarChart } from "react-icons/md";
import { LuIndianRupee } from "react-icons/lu";
import toast from "react-hot-toast";
import { AlertCircle, CreditCard, Lock, X, Mail, Phone } from "lucide-react";
import ProfileHeader from "./ProfileHeader";
import ProfileDetails from "./ProfileDetails";

const CandidateProfile = () => {
  // Persist AI chat state across tab switches
  const [aiMessages, setAiMessages] = useState([]);
  const [aiInput, setAiInput] = useState("");
  const [geminiSummary, setGeminiSummary] = useState("");
  const [geminiSummaryLoading, setGeminiSummaryLoading] = useState(false);
  const links = [
    { text: "Dashboard", url: "/employer/", icon: <HiHome /> },
    {
      text: "Track Candidates",
      url: "/employer/track-candidates",
      icon: <MdBarChart className="h-6 w-6" />,
    },
    {
      text: (
        <span className="flex items-center">
          Source with AI{" "}
          <span className="ml-2 rounded-full bg-orange-400 px-2 text-xs font-semibold text-white">
            Beta
          </span>
        </span>
      ),
      url: "/employer",
      icon: <HiOutlineSparkles />,
      className: "text-orange-400 font-bold hover:text-orange-500",
    },
  ];
  const initialTabs = [
    { key: "score", label: "Visume Score", icon: Star },
    { key: "summary", label: "Summary", icon: HiOutlineUser },
    { key: "experience", label: "Experience", icon: HiBriefcase },
    { key: "education", label: "Education", icon: HiAcademicCap },
    { key: "similar", label: "Similar", icon: HiOutlineSparkles },
  ];
  const [tabOrder, setTabOrder] = useState(initialTabs);
  const [leftTab, setLeftTab] = useState("score");
  const [rightTab, setRightTab] = useState("qa");
  const [subPopup, setSubPopup] = useState(false);
  const [detailsBlur, setDetailsBlur] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [questionsAndAnswers, setQuestionsAndAnswers] = useState([]);
  // Video ref for controlling playback
  const videoRef = useRef(null);

  // Handler to seek and play video at a given timestamp or ISO string
  const handleQuestionClick = (qa) => {
    let seekTime = 0;
    if (
      qa &&
      qa.startTimestamp &&
      Array.isArray(questionsAndAnswers) &&
      questionsAndAnswers.length > 0 &&
      questionsAndAnswers[0].startTimestamp
    ) {
      const base = new Date(questionsAndAnswers[0].startTimestamp);
      const target = new Date(qa.startTimestamp);
      if (!isNaN(base.getTime()) && !isNaN(target.getTime())) {
        seekTime = Math.max(0, (target.getTime() - base.getTime()) / 1000);
      }
    }
    if (videoRef.current) {
      videoRef.current.currentTime = seekTime;
      videoRef.current.play();
    }
  };
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  let { vpid } = useParams();
  const [candidateProf, setCandidateProf] = useState(null);
  const [strippedResumeJson, setStrippedResumeJson] = useState(null);

  // Q&A expand/collapse state for each question
  const [expandedQA, setExpandedQA] = useState({});
  const toggleExpandQA = (idx) => {
    setExpandedQA((prev) => ({
      ...prev,
      [idx]: !prev[idx],
    }));
  };

  // Sidebar open state for mobile/tablet
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Debug: Log sidebar open state at top of render
  console.log("Render CandidateProfile", sidebarOpen);

  // Detect if screen is small (tailwind: < lg)
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  useEffect(() => {
    const checkScreen = () => setIsSmallScreen(window.innerWidth < 1024);
    checkScreen();
    window.addEventListener("resize", checkScreen);
    return () => window.removeEventListener("resize", checkScreen);
  }, []);

  // Prevent background scroll when sidebar is open on small screens
  useEffect(() => {
    if (isSmallScreen && sidebarOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isSmallScreen, sidebarOpen]);
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${vpid}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        // TEMP LOG: Inspect API response and questionsAndAnswers
        console.log(
          "Full API response from /api/v1/video-resume-data/:vpid:",
          data
        );
        console.log(
          "questionsAndAnswers from API response:",
          data?.questionsAndAnswers
        );
        setProfileData(data.data);
        // Use backend-provided timestamps only
        console.log(
          "DEBUG: questionsAndAnswers from API:",
          data.questionsAndAnswers
        );
        setQuestionsAndAnswers(data.questionsAndAnswers || []);

        const additionalResponse = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${
            data.data.cand_id
          }`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (!additionalResponse.ok) {
          throw new Error("Network response for additional data was not ok");
        }
        const additionalData = await additionalResponse.json();
        const candidateProfile = additionalData.candidateProfile[0];
        // Add emp_id from cookies and video_profile_id from vpid
        const emp_id =
          window.Cookies?.get("employerId") ||
          (await import("js-cookie")).default.get("employerId");
        setCandidateProf({
          ...candidateProfile,
          emp_id,
          video_profile_id: vpid || data.data.id,
        });
        // TEMP LOG: Inspect candidateProf structure
        console.log("candidateProf structure:", {
          ...candidateProfile,
          emp_id,
          video_profile_id: vpid || data.data.id,
        });

        const stripped_resume_json = candidateProfile.stripped_resume;
        setStrippedResumeJson(stripped_resume_json);
        // TEMP LOG: Inspect strippedResumeJson structure
        console.log("strippedResumeJson structure:", stripped_resume_json);
        if (!stripped_resume_json) {
          toast.error("Resume Not Uploaded Properly Details are Missing.");
        }
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
        setError("Failed to fetch profile data");
      } finally {
        setLoading(false);
      }
    };

    if (vpid) {
      fetchProfileData();
    }
  }, [vpid]);

  // Fetch Gemini summary if needed (only once per candidate)
  const lastGeminiCandId = useRef(null);
  useEffect(() => {
    const shouldFetchGemini =
      leftTab === "summary" &&
      !strippedResumeJson?.summary &&
      !candidateProf?.summary &&
      !profileData?.summary &&
      candidateProf?.cand_id &&
      !geminiSummary &&
      !geminiSummaryLoading &&
      lastGeminiCandId.current !== candidateProf.cand_id;

    if (shouldFetchGemini) {
      setGeminiSummaryLoading(true);
      lastGeminiCandId.current = candidateProf.cand_id;
      fetch(
        `${import.meta.env.VITE_APP_HOST}/api/gemini-assist/generate-summary`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ cand_id: candidateProf.cand_id }),
        }
      )
        .then((res) => res.json())
        .then((data) => {
          setGeminiSummary(data.summary || "");
        })
        .catch((err) => {
          setGeminiSummary("");
          toast.error("Failed to generate summary with Gemini");
        })
        .finally(() => setGeminiSummaryLoading(false));
    }
  }, [
    leftTab,
    strippedResumeJson,
    candidateProf,
    profileData,
    geminiSummary,
    geminiSummaryLoading,
  ]);

  const profiles = [
    {
      name: "John Doe",
      role: "Front-End Developer",
      rating: 4.5,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Jane Smith",
      role: "UX Designer",
      rating: 4.8,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Alex Johnson",
      role: "Full-Stack Developer",
      rating: 4.2,
      image: "https://via.placeholder.com/50",
    },
  ];
  const navigate = useNavigate();

  if (loading) return <Loader />;
  if (!profileData || !candidateProf) return null;

  return (
    <div className="min-h-screen bg-gray-50">
      <CustomNavbar links={links} />

      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8 mt-20">
        {/* Back Button */}
        <div className="mb-6">
          <button
            onClick={() => {
              const previousUrl = localStorage.getItem("previousUrl");
              if (previousUrl) {
                window.location.href = previousUrl;
              } else {
                navigate("/profile-search/filterCandidate");
              }
            }}
            className="flex items-center space-x-2 font-semibold text-blue-600 transition-colors hover:text-blue-700"
          >
            <svg
              className="h-5 w-5"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to Candidates</span>
          </button>
        </div>

        {/* Mobile Sidebar Toggle */}
        {isSmallScreen && (
          <div className="mb-6 flex justify-end">
            <button
              className="flex items-center space-x-2 rounded-lg bg-blue-600 px-4 py-2 font-semibold text-white transition-colors hover:bg-blue-700"
              onClick={() => setSidebarOpen(true)}
            >
              <svg
                className="h-5 w-5"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              <span>Show Similar Profiles</span>
            </button>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {/* Left Column */}
          <div className="space-y-3">
            <ProfileHeader
              candidateProf={candidateProf}
              profileData={profileData}
              strippedResumeJson={strippedResumeJson}
              questionsAndAnswers={questionsAndAnswers}
              videoRef={videoRef}
            />

            {/* Tab Navigation */}
            <div className="ring-slate-200 overflow-hidden rounded-xl bg-white shadow-sm ring-1">
              <div
                className="scrollbar-hide flex overflow-x-auto"
                style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
              >
                {tabOrder.map((tab, idx) => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.key}
                      className={`flex items-center space-x-2 whitespace-nowrap px-4 py-3 font-semibold transition-colors ${
                        leftTab === tab.key
                          ? "bg-blue-600 text-white"
                          : "text-gray-600 hover:bg-blue-50 hover:text-blue-600"
                      }`}
                      onClick={() => {
                        // Wheel effect: move clicked tab to front, rotate others
                        const newOrder = [
                          ...tabOrder.slice(idx),
                          ...tabOrder.slice(0, idx),
                        ];
                        setTabOrder(newOrder);
                        setLeftTab(tab.key);
                      }}
                    >
                      <IconComponent className="h-5 w-5" />
                      <span>{tab.label}</span>
                    </button>
                  );
                })}
              </div>
              <style>
                {`
                  .scrollbar-hide::-webkit-scrollbar {
                    display: none !important;
                  }
                  .scrollbar-hide {
                    -ms-overflow-style: none !important;
                    scrollbar-width: none !important;
                  }
                `}
              </style>
            </div>

            {/* Tab Content */}
            <div>
              {/* Render left tab content using the same logic as before */}
              {candidateProf?.stripped_resume ? (
                <>
                  {leftTab === "score" && (
                    <div className="ring-slate-200 rounded-xl bg-white p-6 shadow-sm ring-1">
                      <h2 className="mb-6 flex items-center gap-2 text-xl font-bold text-gray-900">
                        <Star className="h-6 w-6 text-yellow-500" />
                        Visume Scores
                      </h2>
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                        {(() => {
                          let scoreObj = {};
                          try {
                            scoreObj =
                              JSON.parse(profileData.score)?.score || {};
                          } catch (e) {}
                          return (
                            <>
                              <ScoreCard
                                icon={
                                  <Star className="h-6 w-6 text-yellow-500" />
                                }
                                title="Skill"
                                score={
                                  scoreObj.Skill_Score ?? scoreObj.Skill ?? 0
                                }
                                color="yellow"
                              />
                              <ScoreCard
                                icon={
                                  <MessageCircle className="h-6 w-6 text-blue-500" />
                                }
                                title="Communication"
                                score={
                                  scoreObj.Communication_Score ??
                                  scoreObj.Communication ??
                                  0
                                }
                                color="blue"
                              />
                              <ScoreCard
                                icon={
                                  <BarChart2 className="h-6 w-6 text-green-500" />
                                }
                                title="Overall"
                                score={
                                  scoreObj.Overall_Score ??
                                  scoreObj.Overall ??
                                  0
                                }
                                color="green"
                              />
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  )}

                  {leftTab === "summary" && (
                    <div className="ring-slate-200 rounded-xl bg-white p-6 shadow-sm ring-1">
                      <h2 className="mb-4 flex items-center gap-2 text-xl font-bold text-gray-900">
                        <HiOutlineUser className="h-6 w-6 text-blue-600" />
                        Professional Summary
                      </h2>
                      {/* Preferred Location and Salary */}
                      {(strippedResumeJson?.personal_info?.location ||
                        strippedResumeJson?.personal_info?.preferred_location ||
                        strippedResumeJson?.personal_info
                          ?.salary_expectation) && (
                        <div className="mb-4 flex flex-wrap gap-4">
                          {strippedResumeJson?.personal_info
                            ?.preferred_location && (
                            <div className="flex items-center gap-2">
                              <HiOutlineGlobeAlt className="h-5 w-5 text-blue-500" />
                              <span className="font-medium text-gray-700">
                                Preferred Location:{" "}
                                {
                                  strippedResumeJson.personal_info
                                    .preferred_location
                                }
                              </span>
                            </div>
                          )}
                          {strippedResumeJson?.personal_info?.location && (
                            <div className="flex items-center gap-2">
                              <HiOutlineGlobeAlt className="h-5 w-5 text-blue-400" />
                              <span className="font-medium text-gray-700">
                                Current Location:{" "}
                                {strippedResumeJson.personal_info.location}
                              </span>
                            </div>
                          )}
                          {strippedResumeJson?.personal_info
                            ?.salary_expectation && (
                            <div className="flex items-center gap-2">
                              <LuIndianRupee className="h-5 w-5 text-green-600" />
                              <span className="font-medium text-gray-700">
                                Salary Expectation:{" "}
                                {
                                  strippedResumeJson.personal_info
                                    .salary_expectation
                                }
                              </span>
                            </div>
                          )}
                        </div>
                      )}
                      <div className="prose prose-gray max-w-none">
                        {strippedResumeJson?.summary ||
                        candidateProf?.summary ||
                        profileData?.summary ? (
                          <div className="leading-relaxed text-gray-700">
                            <ReactMarkdown
                              children={
                                strippedResumeJson?.summary ||
                                candidateProf?.summary ||
                                profileData?.summary
                              }
                              remarkPlugins={[remarkGfm]}
                            />
                          </div>
                        ) : geminiSummaryLoading ? (
                          <p className="italic leading-relaxed text-gray-700">
                            Generating summary with Gemini...
                          </p>
                        ) : geminiSummary ? (
                          <div className="leading-relaxed text-gray-700">
                            <ReactMarkdown
                              children={geminiSummary}
                              remarkPlugins={[remarkGfm]}
                            />
                          </div>
                        ) : (
                          <p className="leading-relaxed text-gray-700">
                            No professional summary available.
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {leftTab === "experience" && (
                    <div className="ring-slate-200 rounded-xl bg-white p-6 shadow-sm ring-1">
                      <h2 className="mb-6 flex items-center gap-2 text-xl font-bold text-gray-900">
                        <HiBriefcase className="h-6 w-6 text-purple-600" />
                        Experience
                      </h2>
                      {/* Skills List */}
                      {strippedResumeJson?.experience &&
                      strippedResumeJson.experience.length > 0 ? (
                        <div className="space-y-6">
                          {strippedResumeJson.experience.map((exp, idx) => (
                            <div
                              key={idx}
                              className="border-l-4 border-purple-200 pb-6 pl-6 last:pb-0"
                            >
                              <div className="mb-2 flex flex-wrap items-center gap-2">
                                <h3 className="text-lg font-bold text-gray-900">
                                  {exp.title || exp.position}
                                </h3>
                                {exp.company && (
                                  <span className="font-medium text-purple-600">
                                    @ {exp.company}
                                  </span>
                                )}
                                {exp.duration && (
                                  <span className="rounded-full bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800">
                                    {exp.duration}
                                  </span>
                                )}
                              </div>
                              {exp.description && (
                                <p className="leading-relaxed text-gray-600">
                                  {exp.description}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="py-3 text-center text-gray-500">
                          No experience details available.
                        </div>
                      )}
                      {/* Skills List BELOW Experience */}
                      {strippedResumeJson &&
                        strippedResumeJson.skills &&
                        Array.isArray(strippedResumeJson.skills.all_skills) &&
                        strippedResumeJson.skills.all_skills.length > 0 && (
                          <div className="mt-8">
                            <h3 className="mb-2 text-lg font-semibold text-gray-800">
                              Skills
                            </h3>
                            <div className="flex flex-wrap gap-2">
                              {strippedResumeJson.skills.all_skills.map(
                                (skill, idx) => (
                                  <span
                                    key={idx}
                                    className="inline-block rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800"
                                  >
                                    {skill}
                                  </span>
                                )
                              )}
                            </div>
                          </div>
                        )}
                    </div>
                  )}

                  {leftTab === "education" && (
                    <div className="ring-slate-200 rounded-xl bg-white p-6 shadow-sm ring-1">
                      <h2 className="mb-6 flex items-center gap-2 text-xl font-bold text-gray-900">
                        <HiAcademicCap className="h-6 w-6 text-green-600" />
                        Education
                      </h2>
                      {strippedResumeJson?.education &&
                      strippedResumeJson.education.length > 0 ? (
                        <div className="space-y-4">
                          {strippedResumeJson.education.map((edu, idx) => (
                            <div
                              key={idx}
                              className="rounded-lg border border-green-200 bg-green-50 p-4"
                            >
                              <div className="flex flex-wrap items-center gap-2">
                                <h3 className="font-bold text-gray-900">
                                  {edu.degree}
                                </h3>
                                {edu.institution && (
                                  <span className="font-medium text-green-700">
                                    @ {edu.institution}
                                  </span>
                                )}
                                {edu.year && (
                                  <span className="rounded bg-green-200 px-2 py-1 text-sm font-medium text-green-800">
                                    {edu.year}
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="py-3 text-center text-gray-500">
                          No education details available.
                        </div>
                      )}
                    </div>
                  )}

                  {leftTab === "similar" && (
                    <div className="ring-slate-200 rounded-xl bg-white p-6 shadow-sm ring-1">
                      <h2 className="mb-6 flex items-center gap-2 text-xl font-bold text-gray-900">
                        <HiOutlineSparkles className="h-6 w-6 text-orange-500" />
                        Similar Profiles
                      </h2>
                      <div className="grid gap-4 md:grid-cols-2">
                        {profiles.map((profile, idx) => (
                          <div
                            key={idx}
                            className="ring-slate-200 hover:bg-slate-50 flex cursor-pointer items-center gap-4 rounded-xl bg-gradient-to-r from-gray-50 to-white p-4 shadow-sm ring-1"
                          >
                            <img
                              src={profile.image}
                              alt={profile.name}
                              className="h-16 w-16 rounded-full border-2 border-gray-200 object-cover"
                            />
                            <div className="flex-1">
                              <h3 className="font-bold text-gray-900">
                                {profile.name}
                              </h3>
                              <p className="mb-1 text-sm text-gray-600">
                                {profile.role}
                              </p>
                              <div className="flex items-center gap-1">
                                <Star className="fill-current h-4 w-4 text-yellow-400" />
                                <span className="text-sm font-medium text-yellow-600">
                                  {profile.rating}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="col-span-2 py-3 text-center text-red-500">
                  Resume details not available.
                </div>
              )}
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Right Tab Navigation */}
            <div className="ring-slate-200 overflow-hidden rounded-xl bg-white shadow-sm ring-1">
              <div className="flex">
                <button
                  className={`flex-1 px-6 py-4 font-semibold transition-colors ${
                    rightTab === "qa"
                      ? "bg-green-600 text-white"
                      : "text-gray-600 hover:bg-green-50 hover:text-green-600"
                  }`}
                  onClick={() => setRightTab("qa")}
                >
                  Interview Q&A
                </button>
                <button
                  className={`flex-1 px-6 py-4 font-semibold transition-colors ${
                    rightTab === "ai"
                      ? "bg-purple-600 text-white"
                      : "text-gray-600 hover:bg-purple-50 hover:text-purple-600"
                  }`}
                  onClick={() => setRightTab("ai")}
                >
                  AI Assistance
                </button>
              </div>
            </div>

            {/* Right Tab Content */}
            <div className={rightTab === "ai" ? "sticky top-6" : ""}>
              {rightTab === "qa" &&
                Array.isArray(questionsAndAnswers) &&
                questionsAndAnswers.length > 0 && (
                  <div className="space-y-4">
                    {questionsAndAnswers.map((qa, idx) => (
                      <div
                        key={idx}
                        className="ring-slate-200 hover:bg-slate-50 overflow-hidden rounded-xl bg-white shadow-sm ring-1"
                      >
                        <div className="p-6">
                          <div
                            className="flex cursor-pointer items-start justify-between"
                            onClick={() => handleQuestionClick(qa)}
                          >
                            <div className="flex-1">
                              <div className="mb-2 flex items-center space-x-2">
                                <span className="rounded-md bg-green-100 px-2 py-1 text-sm font-bold text-green-800">
                                  Q{idx + 1}
                                </span>
                                {qa.startTimestamp && (
                                  <span className="rounded-md bg-blue-100 px-2 py-1 font-mono text-xs text-blue-800">
                                    {(() => {
                                      let sec = 0;
                                      if (
                                        typeof qa.startTimestamp === "number"
                                      ) {
                                        sec = qa.startTimestamp;
                                      } else {
                                        const d = new Date(qa.startTimestamp);
                                        if (!isNaN(d.getTime())) {
                                          sec = Math.floor(
                                            (d.getTime() -
                                              new Date(
                                                questionsAndAnswers[0].startTimestamp
                                              ).getTime()) /
                                              1000
                                          );
                                        }
                                      }
                                      const m = String(
                                        Math.floor(sec / 60)
                                      ).padStart(2, "0");
                                      const s = String(sec % 60).padStart(
                                        2,
                                        "0"
                                      );
                                      return `${m}:${s}`;
                                    })()}
                                  </span>
                                )}
                              </div>
                              <h3 className="mb-3 font-semibold text-gray-900">
                                {qa.question}
                              </h3>
                            </div>
                            <button
                              className="ml-4 text-gray-400 hover:text-gray-600"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleExpandQA(idx);
                              }}
                            >
                              {expandedQA[idx] ? (
                                <svg
                                  className="h-5 w-5"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  viewBox="0 0 24 24"
                                >
                                  <path d="M5 15l7-7 7 7" />
                                </svg>
                              ) : (
                                <svg
                                  className="h-5 w-5"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  viewBox="0 0 24 24"
                                >
                                  <path d="M19 9l-7 7-7-7" />
                                </svg>
                              )}
                            </button>
                          </div>

                          {expandedQA[idx] && (
                            <div className="mt-4 border-t border-gray-200 pt-4">
                              <div className="flex items-start space-x-3">
                                <span className="flex-shrink-0 rounded-md bg-blue-100 px-2 py-1 text-sm font-bold text-blue-800">
                                  A
                                </span>
                                <div className="w-0 min-w-full overflow-x-auto leading-relaxed text-gray-700">
                                  <ReactMarkdown
                                    children={
                                      /<\s*([a-zA-Z]+)[^>]*>/.test(qa.answer)
                                        ? "```html\n" + qa.answer + "\n```"
                                        : qa.answer
                                    }
                                    remarkPlugins={[remarkGfm]}
                                    components={{
                                      code({
                                        node,
                                        inline,
                                        className,
                                        children,
                                        ...props
                                      }) {
                                        const match = /language-(\w+)/.exec(
                                          className || ""
                                        );
                                        return !inline && match ? (
                                          <SyntaxHighlighter
                                            style={oneDark}
                                            language={match[1]}
                                            PreTag="div"
                                            customStyle={{
                                              borderRadius: "0.5rem",
                                              margin: "0.5rem 0",
                                              fontSize: "0.95em",
                                              width: "100%",
                                              boxSizing: "border-box",
                                              overflowX: "auto",
                                            }}
                                            {...props}
                                          >
                                            {String(children).replace(
                                              /\n$/,
                                              ""
                                            )}
                                          </SyntaxHighlighter>
                                        ) : (
                                          <code
                                            className="rounded bg-gray-100 px-1 py-0.5 font-mono text-sm text-pink-700"
                                            {...props}
                                          >
                                            {children}
                                          </code>
                                        );
                                      },
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

              {rightTab === "ai" && (
                <AiAssistance
                  messages={aiMessages}
                  setMessages={setAiMessages}
                  input={aiInput}
                  setInput={setAiInput}
                  context={{
                    candidateProf,
                    profileData,
                    strippedResumeJson,
                    questionsAndAnswers,
                  }}
                />
              )}
            </div>
          </div>
        </div>

        {/* Mobile Sidebar */}
        {isSmallScreen && sidebarOpen && (
          <>
            <div
              className="bg-black fixed inset-0 z-40 bg-opacity-50"
              onClick={() => setSidebarOpen(false)}
            />
            <div className="fixed right-0 top-0 z-50 h-full w-80 max-w-full bg-white shadow-xl">
              <div className="flex items-center justify-between border-b border-gray-200 p-4">
                <h2 className="text-lg font-semibold">Similar Profiles</h2>
                <button
                  className="text-gray-400 hover:text-gray-600"
                  onClick={() => setSidebarOpen(false)}
                >
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="space-y-4 p-4">
                {profiles.map((profile, idx) => (
                  <div
                    key={idx}
                    className="ring-slate-200 hover:bg-slate-50 flex cursor-pointer items-center space-x-3 rounded-xl bg-white p-3 shadow-sm ring-1"
                  >
                    <img
                      src={profile.image}
                      alt={profile.name}
                      className="h-12 w-12 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">
                        {profile.name}
                      </h3>
                      <p className="text-sm text-gray-600">{profile.role}</p>
                      <div className="flex items-center space-x-1">
                        <Star className="fill-current h-3 w-3 text-yellow-400" />
                        <span className="text-xs text-yellow-600">
                          {profile.rating}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Popup Modal */}
        {subPopup && (
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            <div
              className="bg-black absolute inset-0 bg-opacity-50 backdrop-blur-sm"
              onClick={() => setSubPopup(false)}
            />
            <div className="relative w-full max-w-md rounded-2xl bg-white p-6 shadow-2xl">
              <button
                className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
                onClick={() => setSubPopup(false)}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <div className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
                  <AlertCircle className="h-8 w-8 text-yellow-600" />
                </div>
                <h2 className="mb-2 text-xl font-bold text-gray-900">
                  Unlock Profile Confirmation
                </h2>
                <p className="mb-6 text-gray-600">
                  Unlocking this profile will deduct 1 credit from your credit
                  balance. Are you sure you want to proceed?
                </p>
                <div className="flex space-x-3">
                  <button
                    className="flex-1 rounded-lg border border-gray-300 px-4 py-2 font-semibold text-gray-700 transition-colors hover:bg-gray-50"
                    onClick={() => setSubPopup(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="flex-1 rounded-lg bg-blue-600 px-4 py-2 font-semibold text-white transition-colors hover:bg-blue-700"
                    onClick={() => setSubPopup(false)}
                  >
                    Unlock Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

import AiAssistance from "./AiAssistance";

export default CandidateProfile;
