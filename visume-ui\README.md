# Visume UI

Visume UI is the frontend application for the Visume platform, providing a modern interface for both employers and candidates.

---

## Table of Contents

- [About](#about)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Environment Variables](#environment-variables)
  - [Running the App](#running-the-app)
  - [Build for Production](#build-for-production)
- [API Integration](#api-integration)
- [Available Scripts](#available-scripts)
- [Contributing](#contributing)
- [License](#license)

---

## About

Visume is a platform connecting employers and candidates through AI-powered video resumes and intelligent job matching.

- **For Candidates:**  
  Candidates can create a Visume (video resume) by providing details such as desired role, skills, company size, and experience. The platform conducts an AI-driven interview, records the candidate's responses, and generates a detailed evaluation with a score.

- **For Employers:**  
  Employers can create job descriptions specifying requirements. The system automatically matches and displays relevant candidate profiles (with video resumes and AI scores), enabling employers to shortlist, review, and manage candidates efficiently.

---

## Features

- AI-powered video resume creation for candidates
- Automated AI interviews with scoring and feedback
- Employer dashboard to create job descriptions and view matched candidates
- Intelligent candidate-job matching based on skills, experience, and requirements
- Shortlisting and candidate management tools for employers
- Waitlist and onboarding flows
- Responsive design for all devices
- Modular component-based architecture

---

## Tech Stack

- **React** (frontend framework)
- **JavaScript** (ES6+)
- **CSS** (custom and utility classes)
- **Axios** (API requests)
- **Netlify** (deployment, optional)

---

## Project Structure

```
visume-ui/
├── public/                 # Static assets
├── src/
│   ├── api/                # API utility functions
│   ├── assets/             # Images, SVGs, CSS
│   ├── components/         # Reusable UI components
│   ├── hooks/              # Custom React hooks
│   ├── layouts/            # Layout components
│   ├── utils/              # Utility functions
│   ├── variables/          # Theme and config variables
│   └── views/              # Page-level components
├── .env.example            # Example environment variables
├── package.json            # Project metadata and scripts
└── README.md               # Project documentation
```

---

## Getting Started

### Prerequisites

- Node.js (v16 or above)
- npm (v8 or above)

### Installation

Clone the repository and install dependencies:

```bash
git clone https://github.com/your-org/visume-ui.git
cd visume-ui
npm install
```

### Environment Variables

Copy `.env.example` to `.env` and update the values as needed:

```bash
cp .env.example .env
```

- `REACT_APP_API_URL`: URL of the backend API (e.g., http://localhost:5000)
- `REACT_APP_GOOGLE_CLIENT_ID`: Google OAuth client ID
- `REACT_APP_ENV`: Environment (development/production)

### Running the App

Start the development server:

```bash
npm run start
```

The app will be available at [http://localhost:3000](http://localhost:3000).

### Build for Production

To create a production build:

```bash
npm run build
```

The build output will be in the `build/` directory.

---

## API Integration

- All API requests are configured in [`src/api/axios.js`](src/api/axios.js:1).
- Ensure `REACT_APP_API_URL` in your `.env` points to the correct backend.
- Authentication and protected routes use JWT tokens (if enabled in backend).

---

## Available Scripts

- `npm run start` — Start development server
- `npm run build` — Build for production
- `npm run lint` — Lint codebase (if configured)
- `npm run format` — Format code (if configured)

---

## Contributing

1. Fork the repository
2. Create a new branch (`git checkout -b feature/your-feature`)
3. Commit your changes
4. Push to your fork and submit a pull request

---

## License

This project is licensed. See the main repository for details.