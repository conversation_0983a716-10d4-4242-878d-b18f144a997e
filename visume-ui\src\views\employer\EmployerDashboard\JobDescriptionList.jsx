import React, {
  useEffect,
  useState,
  useImperative<PERSON>andle,
  forwardRef,
} from "react";
import { useNavigate } from "react-router-dom";
import { Modal } from "react-responsive-modal";
import "react-responsive-modal/styles.css";

import Card from "../../../components/card";
import { HiOutlineSparkles } from "react-icons/hi";
const JobDescriptionList = forwardRef(({ emp_id, onUploadClick }, ref) => {
  const [jobDescriptions, setJobDescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [skillsOpenIdx, setSkillsOpenIdx] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [jobToDelete, setJobToDelete] = useState(null);
  const navigate = useNavigate();

  const handleDelete = async (id) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${id}`,
        { method: "DELETE" }
      );
      if (response.ok) {
        setJobDescriptions((prev) => prev.filter((jd) => jd._id !== id));
      } else {
        alert("Failed to delete job description.");
      }
    } catch {
      alert("Error deleting job description.");
    }
    setDeleteModalOpen(false);
    setJobToDelete(null);
  };

  const openDeleteModal = (id) => {
    setJobToDelete(id);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setJobToDelete(null);
  };

  const fetchJobDescriptions = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
      );
      if (response.ok) {
        const data = await response.json();
        setJobDescriptions(data.jobDescriptions || []);
      }
    } catch (err) {
      setJobDescriptions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (emp_id) fetchJobDescriptions();
  }, [emp_id]);

  useImperativeHandle(ref, () => ({
    refresh: fetchJobDescriptions,
  }));

  if (loading) return <div>Loading job descriptions...</div>;
  if (!jobDescriptions.length)
    return (
      <Card extra="rounded-2xl border border-gray-200 bg-gradient-to-br from-white via-gray-50 to-gray-100 shadow-xl p-8 flex flex-col items-center justify-center">
        <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
          All Job Descriptions
        </h3>
        <p className="mb-6 text-center text-gray-600">
          Upload a job description to find matching candidates using AI.
        </p>
        <button
          onClick={onUploadClick}
          className="inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-sm font-semibold text-white shadow-lg transition-all hover:bg-blue-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <HiOutlineSparkles className="mr-2 h-4 w-4" />
          Upload Job Description
        </button>
      </Card>
    );

  return (
    <div className="mb-6">
      <Modal
        open={deleteModalOpen}
        onClose={closeDeleteModal}
        center
        classNames={{ modal: "rounded-2xl p-8 max-w-md w-full" }}
        animationDuration={200}
      >
        <div className="flex flex-col items-center">
          <div className="mb-4 text-2xl font-bold text-red-600">
            Confirm Delete
          </div>
          <div className="mb-6 text-center text-gray-700">
            Are you sure you want to delete this job description? This action
            cannot be undone.
          </div>
          <div className="flex gap-4">
            <button
              className="rounded-lg bg-red-600 px-6 py-2 font-semibold text-white shadow transition hover:bg-red-700"
              onClick={() => handleDelete(jobToDelete)}
            >
              Yes, Delete
            </button>
            <button
              className="rounded-lg bg-gray-200 px-6 py-2 font-semibold text-gray-800 shadow transition hover:bg-gray-300"
              onClick={closeDeleteModal}
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
          All Job Descriptions
        </h3>
        <button
          onClick={onUploadClick}
          className="inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-sm font-semibold text-white shadow-lg transition-all hover:bg-blue-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <HiOutlineSparkles className="mr-2 h-4 w-4" />
          Upload Job Description
        </button>
      </div>
      <div className="max-h-96 overflow-auto">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {jobDescriptions.map((jd, idx) => (
            <div
              key={jd._id || idx}
              className="mb-6 flex w-auto min-w-fit max-w-[350px] flex-col justify-between rounded-2xl border border-gray-200 bg-gradient-to-br from-white via-gray-50 to-gray-100 p-5 shadow-xl transition-all duration-200 hover:shadow-2xl dark:from-navy-900 dark:via-navy-800 dark:to-navy-700"
              title="View matching candidates"
            >
              {/* Header Row */}
              <div className="mb-2 flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-xl font-bold text-blue-600">
                  {jd.role ? jd.role[0].toUpperCase() : "J"}
                </div>
                <div className="flex min-w-0 flex-col">
                  <span className="truncate text-lg font-bold text-gray-900 dark:text-white">
                    {jd.role || "No Role"}
                  </span>
                  <span className="text-xs text-gray-500">
                    {Array.isArray(jd.location)
                      ? jd.location.join(", ")
                      : jd.location}
                  </span>
                </div>
              </div>
              {/* Info Chips */}
              <div className="mb-2 flex flex-wrap gap-2">
                <span className="inline-flex items-center gap-1 rounded-full border border-blue-200 bg-blue-50 px-3 py-0.5 text-xs font-semibold text-blue-700">
                  Exp: {jd.experience}
                </span>
                <span
                  className="relative inline-flex cursor-pointer items-center gap-1 rounded-full border border-indigo-200 bg-indigo-50 px-3 py-0.5 text-xs font-semibold text-indigo-700"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSkillsOpenIdx(skillsOpenIdx === idx ? null : idx);
                  }}
                >
                  Skills
                  <svg
                    className={`ml-1 h-4 w-4 transition-transform ${
                      skillsOpenIdx === idx ? "rotate-90" : "rotate-0"
                    }`}
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                  {skillsOpenIdx === idx && (
                    <div className="absolute left-0 top-8 z-10 min-w-[120px] rounded-xl border border-indigo-200 bg-white p-3 text-xs text-gray-700 shadow-xl dark:bg-navy-800 dark:text-white">
                      {(Array.isArray(jd.skills) ? jd.skills : [jd.skills]).map(
                        (skill, i) => (
                          <div
                            key={i}
                            className="rounded px-2 py-1 hover:bg-indigo-50"
                          >
                            {skill}
                          </div>
                        )
                      )}
                    </div>
                  )}
                </span>
              </div>
              {/* Action Button */}
              <div className="mt-4 flex gap-3 border-t border-gray-200 pt-4">
                <button
                  className="flex flex-1 items-center justify-center gap-2 rounded-lg bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm transition-colors hover:bg-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    openDeleteModal(jd._id);
                  }}
                  title="Delete"
                >
                  Delete
                </button>
                <button
                  className="flex flex-1 items-center justify-center gap-2 rounded-lg border border-indigo-200 bg-white px-3 py-2 text-sm font-semibold text-indigo-700 shadow-sm transition-colors hover:bg-indigo-50 hover:text-indigo-900"
                  onClick={() =>
                    navigate(`/employer/job-description/${jd._id}`, {
                      state: { job: jd },
                    })
                  }
                  title="View Details"
                >
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

export default JobDescriptionList;
