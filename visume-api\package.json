{"name": "backend", "version": "1.0.0", "description": "zoomjobs api", "main": "app.js", "type": "commonjs", "scripts": {"start:app": "node app.js", "start:dev": "nodemon app.js", "seed": "node prisma/seed.js"}, "prisma": {"seed": "node prisma/seed.js"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.816.0", "@aws-sdk/s3-request-presigner": "^3.816.0", "@google/generative-ai": "^0.24.1", "@prisma/client": "^6.8.2", "aws-sdk": "^2.1691.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.4.5", "express": "^4.19.2", "file-type": "^19.6.0", "google-auth-library": "^10.1.0", "groq-sdk": "^0.23.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.5", "nodemon": "^3.1.7", "pdf-parse": "^1.1.1"}, "devDependencies": {"concurrently": "^8.2.2", "prisma": "^6.8.2"}}