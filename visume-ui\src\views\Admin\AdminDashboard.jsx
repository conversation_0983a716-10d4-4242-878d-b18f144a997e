import React, { useState, useEffect } from 'react';
import {
  Users,
  Building2,
  CreditCard,
  Crown,
  BarChart3,
  TrendingUp,
  UserCheck,
  Briefcase,
  Calendar,
  Activity
} from "lucide-react";
import { HiOutlineSparkles } from "react-icons/hi";
import EmployerManagement from "./components/EmployerManagement";
import CandidateManagement from "./components/CandidateManagement";

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState({
    totalEmployers: 0,
    totalCandidates: 0,
    activeEmployers: 0,
    activeCandidates: 0,
    totalCreditsUsed: 0,
    totalVisumesCreated: 0
  });
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock stats data
        setStats({
          totalEmployers: 156,
          totalCandidates: 2847,
          activeEmployers: 89,
          activeCandidates: 1923,
          totalCreditsUsed: 1247,
          totalVisumesCreated: 3891
        });
      } catch (error) {
        console.error("Error fetching stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // Enhanced Metric Card Component with Membership Management Styling
  const MetricCard = ({ title, value, icon: Icon, trend, color }) => (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-2xl hover:scale-105 group overflow-hidden">
      <div className="flex flex-col items-center text-center">
        <div 
          className="p-4 rounded-xl shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300"
          style={{
            background: `linear-gradient(135deg, ${color}, ${color}dd)`,
            boxShadow: `0 4px 20px ${color}40`
          }}
        >
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 font-manrope">
            {title}
          </p>
          <p className="text-3xl font-bold text-gray-900 dark:text-white mb-2 font-sora">
            {loading ? (
              <div className="animate-pulse h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded mx-auto"></div>
            ) : (
              value.toLocaleString()
            )}
          </p>
          {trend && (
            <div className="flex items-center justify-center">
              <TrendingUp className="w-4 h-4 mr-1 text-green-500" />
              <span className="text-sm font-medium text-green-500 font-manrope">
                +{trend}% from last month
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // Enhanced Tab Button Component with Membership Management Styling
  const TabButton = ({ id, label, icon: Icon, isActive, onClick, count }) => (
    <button
      onClick={() => onClick(id)}
      className={`
        flex items-center gap-3 px-6 py-4 rounded-xl font-medium transition-all duration-300 font-manrope
        ${isActive
          ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-xl transform scale-105'
          : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:scale-102 hover:bg-gray-50 dark:hover:bg-gray-700'
        }
      `}
    >
      <Icon className="w-5 h-5" />
      <span className="font-semibold">{label}</span>
      {count !== undefined && (
        <span
          className={`
            px-3 py-1 rounded-full text-xs font-bold
            ${isActive
              ? 'bg-white/20 text-white'
              : 'bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-600 dark:text-blue-400'
            }
          `}
        >
          {count}
        </span>
      )}
    </button>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-12">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-3 font-sora">
                  Admin Dashboard
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-manrope">
                  Manage platform users and monitor membership statistics
                </p>
              </div>
              <div className="hidden md:flex items-center space-x-4">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-xl">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-12">
          <MetricCard
            title="Total Employers"
            value={stats.totalEmployers}
            icon={Building2}
            color="#3B82F6"
            trend={12}
          />
          <MetricCard
            title="Active Employers"
            value={stats.activeEmployers}
            icon={UserCheck}
            color="#10B981"
            trend={8}
          />
          <MetricCard
            title="Total Candidates"
            value={stats.totalCandidates}
            icon={Users}
            color="#8B5CF6"
            trend={15}
          />
          <MetricCard
            title="Active Candidates"
            value={stats.activeCandidates}
            icon={Briefcase}
            color="#F59E0B"
            trend={10}
          />
          <MetricCard
            title="Visumes Created"
            value={stats.totalVisumesCreated}
            icon={HiOutlineSparkles}
            color="#06B6D4"
            trend={18}
          />
        </div>

        {/* Tab Navigation */}
        <div className="flex flex-col sm:flex-row gap-6 mb-12">
          <TabButton
            id="overview"
            label="Overview"
            icon={BarChart3}
            isActive={activeTab === "overview"}
            onClick={setActiveTab}
          />
          <TabButton
            id="employers"
            label="Employer Management"
            icon={Building2}
            isActive={activeTab === "employers"}
            onClick={setActiveTab}
            count={stats.totalEmployers}
          />
          <TabButton
            id="candidates"
            label="Candidate Management"
            icon={Users}
            isActive={activeTab === "candidates"}
            onClick={setActiveTab}
            count={stats.totalCandidates}
          />
        </div>

        {/* Tab Content */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700">
          {activeTab === "overview" && (
            <div className="p-8">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white font-sora">
                  Platform Overview
                </h2>
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl">
                  <Activity className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* Recent Activity Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-8 rounded-2xl border border-gray-200 dark:border-gray-600 shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3 font-sora">
                    <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-2 rounded-lg">
                      <Activity className="w-5 h-5 text-white" />
                    </div>
                    Recent Activity
                  </h3>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                        New employer registrations
                      </span>
                      <span className="font-bold text-green-600 dark:text-green-400 font-manrope bg-green-50 dark:bg-green-900/30 px-3 py-1 rounded-full">
                        +12 today
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                        New candidate profiles
                      </span>
                      <span className="font-bold text-blue-600 dark:text-blue-400 font-manrope bg-blue-50 dark:bg-blue-900/30 px-3 py-1 rounded-full">
                        +45 today
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
                      <span className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                        Credits consumed
                      </span>
                      <span className="font-bold text-purple-600 dark:text-purple-400 font-manrope bg-purple-50 dark:bg-purple-900/30 px-3 py-1 rounded-full">
                        89 today
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-8 rounded-2xl border border-gray-200 dark:border-gray-600 shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3 font-sora">
                    <div className="bg-gradient-to-r from-orange-500 to-red-600 p-2 rounded-lg">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    Quick Actions
                  </h3>
                  <div className="space-y-4">
                    <button
                      onClick={() => setActiveTab("employers")}
                      className="w-full text-left p-6 rounded-xl transition-all duration-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg hover:scale-102 group"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white font-manrope mb-1">
                            Manage Employer Credits
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                            View and adjust credit balances
                          </p>
                        </div>
                        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                          <Building2 className="w-5 h-5 text-white" />
                        </div>
                      </div>
                    </button>
                    <button
                      onClick={() => setActiveTab("candidates")}
                      className="w-full text-left p-6 rounded-xl transition-all duration-300 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg hover:scale-102 group"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white font-manrope mb-1">
                            Manage Candidate Limits
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 font-manrope">
                            Adjust Visume creation limits
                          </p>
                        </div>
                        <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                          <Users className="w-5 h-5 text-white" />
                        </div>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "employers" && <EmployerManagement />}
          {activeTab === "candidates" && <CandidateManagement />}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;