// 🎯 EMPLOYER MEMBERSHIP SERVICE: Handle employer membership-related API calls

/**
 * Get employer's membership status including credits, plan info, and upgrade eligibility
 * @param {string|number} empId - Employer ID
 * @returns {Promise<Object>} Membership status object
 */
export const getEmployerMembershipStatus = async (empId) => {
  try {
    const response = await fetch(
      `${import.meta.env.VITE_APP_HOST}/api/v1/employer-membership/${empId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.membershipStatus;
  } catch (error) {
    console.error('Error fetching employer membership status:', error);
    throw error;
  }
};

/**
 * Validate if employer can use credits before performing credit-consuming actions
 * @param {string|number} empId - Employer ID
 * @returns {Promise<Object>} Validation result with canUseCredits flag and membership info
 */
export const validateEmployerCreditUsage = async (empId) => {
  try {
    const membershipStatus = await getEmployerMembershipStatus(empId);
    
    return {
      canUseCredits: membershipStatus.canUseCredits,
      membershipStatus,
      needsUpgrade: membershipStatus.needsUpgrade
    };
  } catch (error) {
    console.error('Error validating employer credit usage:', error);
    return {
      canUseCredits: false,
      membershipStatus: {
        currentCredits: 0,
        planName: 'Error',
        statusText: 'Error checking membership status',
        statusColor: 'error',
        needsUpgrade: true
      },
      needsUpgrade: true
    };
  }
};

/**
 * Format membership status for UI display
 * @param {Object} membershipStatus - Raw membership status from API
 * @returns {Object} Formatted status for UI components
 */
export const getFormattedEmployerMembershipStatus = (membershipStatus) => {
  if (!membershipStatus) {
    return {
      planName: 'Free Employer Plan',
      currentCredits: 0,
      statusText: 'No membership data available',
      statusColor: 'error',
      canUseCredits: false,
      needsUpgrade: true
    };
  }

  return {
    planName: membershipStatus.planName || 'Free Employer Plan',
    currentCredits: membershipStatus.currentCredits || 0,
    statusText: membershipStatus.statusText || 'No credits remaining',
    statusColor: membershipStatus.statusColor || 'error',
    canUseCredits: membershipStatus.canUseCredits || false,
    needsUpgrade: membershipStatus.needsUpgrade || false,
    // Additional formatting for UI
    creditsDisplay: `${membershipStatus.currentCredits || 0} credit${(membershipStatus.currentCredits || 0) !== 1 ? 's' : ''} remaining`,
    usagePercentage: membershipStatus.currentCredits > 0 ? 
      Math.max(0, ((10 - membershipStatus.currentCredits) / 10) * 100) : 100
  };
};
