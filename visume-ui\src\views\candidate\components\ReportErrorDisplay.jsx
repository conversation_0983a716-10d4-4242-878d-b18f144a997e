import React from "react";
import { Alert<PERSON>riangle, Mail, Home } from "lucide-react";
import { Link } from "react-router-dom";

function ReportErrorDisplay() {
  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-2xl w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-8 text-center">
        <div className="mb-6">
          <AlertTriangle className="mx-auto h-20 w-20 text-red-500 dark:text-red-400" />
        </div>
        <h1 className="mb-3 text-3xl font-bold text-gray-900 dark:text-white">
          Report Generation Failed
        </h1>
        <p className="mb-6 text-lg text-gray-600 dark:text-gray-400">
          We encountered a technical issue while generating your video resume report.
          Please try again later or contact our support team for assistance.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <a
            href="https://wa.me/6364361448"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center justify-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-semibold text-white shadow-md transition-all duration-200 hover:bg-blue-700 hover:shadow-lg"
          >
            <Mail className="h-5 w-5" />
            WhatsApp Support
          </a>
          <Link
            to="/candidate/dashboard"
            className="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-6 py-3 font-semibold text-gray-700 shadow-md transition-all duration-200 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            <Home className="h-5 w-5" />
            Go to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}

export default ReportErrorDisplay;