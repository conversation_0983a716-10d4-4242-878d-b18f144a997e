"use client"
import { motion } from "framer-motion"
import { useState } from "react"
import WaitlistModal from "./WaitlistModal"

const Contact = () => {
  const [waitlistOpen, setWaitlistOpen] = useState(false);
  const openWaitlistModal = () => setWaitlistOpen(true);
  const closeWaitlistModal = () => setWaitlistOpen(false);

  return (
    <>
      <WaitlistModal open={waitlistOpen} onClose={closeWaitlistModal} />
      <section className="py-6 lg:py-8 bg-white relative">
        <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <motion.div
          className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 shadow-2xl"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          {/* Background Image Layer */}
          <div className="absolute inset-0 z-0">
            <img
              src="/contactusbg.png"
              alt="Contact Us Background"
              className="h-full w-full object-cover object-center opacity-60 mix-blend-multiply"
              draggable="false"
            />
          </div>
          {/* Decorative Background Circles removed */}
          {/* Content */}
          <div className="relative z-20 px-8 py-16 lg:px-16 lg:py-20">
            <div className="max-w-2xl">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2, duration: 0.8 }}
              >
                <h2 className="mb-6 text-4xl font-bold leading-tight text-white lg:text-5xl">Let's Get In Touch.</h2>
                <p className="mb-4 text-xl leading-relaxed text-blue-100">
                  Be the first to experience the future of video-based hiring.
                </p>
                <p className="mb-12 text-lg leading-relaxed text-blue-200">
                  Join our waitlist and get early access to features that will <br />
                  transform how you connect with talent.
                </p>
              </motion.div>
              <motion.div
                className="flex gap-4"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.8 }}
              >
                <motion.button
                  className="group flex items-center justify-center rounded-xl bg-white px-8 py-4 text-lg font-semibold text-blue-600 shadow-lg transition-all duration-300 hover:bg-gray-50"
                  whileHover={{
                    scale: 1.02,
                    boxShadow: "0 15px 35px rgba(0,0,0,0.15)",
                  }}
                  whileTap={{ scale: 0.98 }}
                  onClick={openWaitlistModal}
                >
                  <span>Join Waitlist</span>
                  <motion.div
                    className="ml-2 h-2 w-2 rounded-full bg-blue-600"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{
                      duration: 2,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "easeInOut",
                    }}
                  />
                </motion.button>
              </motion.div>
            </div>
          </div>
          {/* Subtle Pattern Overlay removed */}
        </motion.div>
      </div>
    </section>
    </>
  )
}

export default Contact
