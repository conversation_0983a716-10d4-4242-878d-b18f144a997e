// InterviewSectionContent.jsx
import React from "react";
import VideoPreview from "../../smaller_comp/VideoPreview";
import QuestionDisplay from "../../smaller_comp/QuestionDisplay";
import AnswerInput from "../../smaller_comp/AnswerInput";
import { Mic, Video } from "lucide-react";

export default function InterviewSectionContent({
  localCamStream,
  videoRef,
  questions,
  currentQuestion,
  currentIndex,
  isSpeaking,
  isListening,
  isProcessing,
  isInterviewActive,
  isTransitioning,
  error,
  isLoading,
  remainingTime,
  handleSpeechRecognition,
  handleNextQuestion,
  handleEndInterview,
  startAnswering,
  updateAnswer,

}) {
  // Error and loading UI (copied from InterviewSection)
  const errorUI = (
    <div className="flex h-screen items-center justify-center">
      <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
        <h2 className="mb-4 text-xl font-semibold text-red-700">
          Interview Setup Error
        </h2>
        <p className="text-red-600">{error}</p>
        <div className="mt-6 flex justify-center space-x-4">
          <button
            onClick={() => (window.location.href = "/candidate/dashboard")}
            className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
          >
            Return to Dashboard
          </button>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
          >
            Retry
          </button>
        </div>
      </div>
    </div>
  );

  if (error) return errorUI;

  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
          <h2 className="mb-4 text-xl font-semibold text-red-700">
            Interview Setup Error
          </h2>
          <p className="text-red-600">No interview questions available</p>
          <div className="mt-6 flex justify-center space-x-4">
            <button
              onClick={() => window.location.reload()}
              className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (
    typeof currentIndex !== "number" ||
    currentIndex < 0
  ) {
    if (isTransitioning || isLoading) {
      return null;
    }
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
          <h2 className="mb-4 text-xl font-semibold text-red-700">
            Question Loading Error
          </h2>
          <p className="text-red-600">Failed to load current question</p>
          <div className="mt-6 flex justify-center space-x-4">
            <button
              onClick={() => window.location.reload()}
              className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }
  if (typeof currentIndex === "number" && questions && Array.isArray(questions) && currentIndex >= questions.length) {
    return null;
  }

  return (
    <div className="min-h-[calc(100vh-80px)] bg-gray-50 dark:bg-gray-900">
      <div className="mx-auto w-[95vw] p-6">
        {/* Main Interview Layout - Flex Row with 60-40 Split */}
        <div className="flex gap-6 h-[calc(100vh-110px)]">
          {/* Video Preview and Question Display - 60% Width */}
          <div className="w-[60%] flex flex-col gap-2">
            {/* Video Preview - 60% of left panel */}
            <div className="flex-[4] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm overflow-hidden">
              <div className="h-[calc(100%)]">
                {localCamStream && <VideoPreview stream={localCamStream} videoRef={videoRef} />}
              </div>
            </div>

            {/* Question Display - 40% of left panel */}
            <div className="flex-[2] min-h-0">
              <QuestionDisplay
                question={
                  (questions && questions[currentIndex]
                    ? questions[currentIndex].question
                    : null) ||
                  currentQuestion?.question ||
                  ""
                }
                followUp={
                  (questions && questions[currentIndex]
                    ? questions[currentIndex].follow_up
                    : null) || currentQuestion?.follow_up
                }
                currentAnalysis={null}
                isSpeaking={isSpeaking}
                isListening={isListening}
                isLoading={isProcessing || isLoading}
              />
            </div>
          </div>

          {/* Answer Input Panel - 40% Width */}
          <div className="w-[40%]">
            {currentQuestion && questions && questions[currentIndex] && (
              <AnswerInput
                question={currentQuestion}
                handleSpeechRecognition={handleSpeechRecognition}
                isListening={isListening}
                handleNextQuestion={handleNextQuestion}
                onEndInterview={handleEndInterview}
                currentIndex={currentIndex}
                isSpeaking={isSpeaking}
                remainingTime={remainingTime}
                isProcessing={isProcessing || isLoading}
                startAnswering={startAnswering}
                updateAnswer={updateAnswer}
                isInterviewActive={isInterviewActive}
                questions={questions}
                // Phase 1: Removed requirement status props to simplify interview flow
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
