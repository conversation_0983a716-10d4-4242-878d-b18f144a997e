const prisma = require("../config/prisma");

// 🎯 EMPLOYER MEMBERSHIP VALIDATION: Helper function to check employer's credit status
const checkEmployerMembershipStatus = async (empId) => {
  try {
    // Get employer plan details
    const employerPlan = await prisma.employerplans.findFirst({
      where: { emp_id: Number(empId) },
      include: {
        plans: true
      }
    });

    if (!employerPlan) {
      return {
        canUseCredits: false,
        currentCredits: 0,
        planName: 'No Plan',
        statusText: 'No active plan found',
        statusColor: 'error',
        needsUpgrade: true
      };
    }

    const currentCredits = employerPlan.creditsLeft || 0;
    const planName = employerPlan.plans.plan_name || 'Unknown Plan';
    const canUseCredits = currentCredits > 0;

    return {
      canUseCredits,
      currentCredits,
      planName,
      statusText: canUseCredits
        ? `${currentCredits} credit${currentCredits !== 1 ? 's' : ''} remaining`
        : 'No credits remaining - upgrade needed',
      statusColor: canUseCredits ? 'success' : 'error',
      needsUpgrade: !canUseCredits
    };
  } catch (error) {
    console.error('Error checking employer membership status:', error);
    return {
      canUseCredits: false,
      currentCredits: 0,
      planName: 'Error',
      statusText: 'Error checking membership status',
      statusColor: 'error',
      needsUpgrade: true
    };
  }
};

const employerController = {
  // Check employer email uniqueness endpoint
  checkEmployerEmailUniqueness: async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ message: 'Email is required' });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: 'Invalid email format' });
      }

      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return res.status(409).json({
          message: 'Email already exists. Please log in.',
          field: 'email',
          available: false
        });
      }

      return res.status(200).json({
        message: 'Email is available',
        available: true
      });
    } catch (err) {
      console.error('Error checking employer email uniqueness:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },

  // 🎯 EMPLOYER MEMBERSHIP STATUS: Get employer's membership and credit information
  getEmployerMembershipStatus: async (req, res) => {
    try {
      const { emp_id } = req.params;

      if (!emp_id) {
        return res.status(400).json({ message: 'Employer ID is required' });
      }

      const membershipStatus = await checkEmployerMembershipStatus(emp_id);

      return res.status(200).json({
        message: 'Employer membership status retrieved successfully',
        membershipStatus
      });
    } catch (err) {
      console.error('Error getting employer membership status:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },
};

module.exports = employerController;
