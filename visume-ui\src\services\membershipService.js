/**
 * Membership Service - Handles all membership-related API calls
 */

const API_BASE_URL = import.meta.env.VITE_APP_HOST;

/**
 * Fetch candidate's membership status and Visume limits
 * @param {string} candId - Candidate ID
 * @returns {Promise<Object>} Membership status data
 */
export async function getCandidateMembershipStatus(candId) {
  if (!candId) {
    throw new Error('Candidate ID is required');
  }

  try {
    const response = await fetch(
      `${API_BASE_URL}/api/v1/candidate-membership/${candId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data.data;
  } catch (error) {
    console.error('Error fetching membership status:', error);
    throw error;
  }
}

/**
 * Check if candidate can create a new Visume
 * @param {string} candId - Candidate ID
 * @returns {Promise<boolean>} Whether candidate can create a new Visume
 */
export async function canCandidateCreateVisume(candId) {
  try {
    const membershipStatus = await getCandidateMembershipStatus(candId);
    return membershipStatus.canCreateVisume;
  } catch (error) {
    console.error('Error checking Visume creation eligibility:', error);
    // Default to allowing creation on error (fail-safe)
    return true;
  }
}

/**
 * Get formatted membership status for UI display
 * @param {string} candId - Candidate ID
 * @returns {Promise<Object>} Formatted membership data for UI
 */
export async function getFormattedMembershipStatus(candId) {
  try {
    const status = await getCandidateMembershipStatus(candId);
    
    return {
      ...status,
      usagePercentage: (status.currentVisumeCount / status.allowedVisumes) * 100,
      remainingVisumes: Math.max(0, status.allowedVisumes - status.currentVisumeCount),
      isAtLimit: status.currentVisumeCount >= status.allowedVisumes,
      statusText: status.canCreateVisume 
        ? `${status.currentVisumeCount} of ${status.allowedVisumes} Visumes used`
        : 'Visume limit reached',
      statusColor: status.canCreateVisume 
        ? (status.currentVisumeCount / status.allowedVisumes > 0.8 ? 'warning' : 'success')
        : 'error'
    };
  } catch (error) {
    console.error('Error formatting membership status:', error);
    // Return default status on error
    return {
      canCreateVisume: true,
      currentVisumeCount: 0,
      allowedVisumes: 1,
      planName: 'Free Plan',
      hasActivePlan: false,
      needsUpgrade: false,
      usagePercentage: 0,
      remainingVisumes: 1,
      isAtLimit: false,
      statusText: 'Loading...',
      statusColor: 'neutral',
      error: true
    };
  }
}

/**
 * Handle membership limit reached scenario
 * @param {Object} membershipStatus - Current membership status
 * @returns {Object} Error response for limit reached
 */
export function createLimitReachedResponse(membershipStatus) {
  return {
    success: false,
    limitReached: true,
    message: "You have used your 1 free Visume. To create more Visumes, please contact us.",
    membershipStatus,
    whatsappContact: "[WHATSAPP_NUMBER_PLACEHOLDER]"
  };
}

/**
 * Validate membership before Visume creation
 * @param {string} candId - Candidate ID
 * @returns {Promise<Object>} Validation result
 */
export async function validateVisumeCreation(candId) {
  try {
    const membershipStatus = await getCandidateMembershipStatus(candId);
    
    if (!membershipStatus.canCreateVisume) {
      return createLimitReachedResponse(membershipStatus);
    }
    
    return {
      success: true,
      limitReached: false,
      membershipStatus
    };
  } catch (error) {
    console.error('Error validating Visume creation:', error);
    // Allow creation on validation error (fail-safe)
    return {
      success: true,
      limitReached: false,
      error: true,
      message: 'Unable to validate membership status'
    };
  }
}

export default {
  getCandidateMembershipStatus,
  canCandidateCreateVisume,
  getFormattedMembershipStatus,
  createLimitReachedResponse,
  validateVisumeCreation
};
