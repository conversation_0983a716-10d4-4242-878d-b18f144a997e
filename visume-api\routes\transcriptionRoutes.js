const express = require('express');
const multer = require('multer');
const fs = require('fs');
const { Groq } = require('groq-sdk');
const path = require('path');

const router = express.Router();

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '..', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for handling file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir)
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '.webm')
  }
});

const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'audio/webm') {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type, only audio/webm is allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Initialize Groq client
if (!process.env.GROQ_API_KEY) {
  console.error("GROQ_API_KEY not found in environment variables");
}
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY
});

router.post("/transcribe", upload.single("file"), async (req, res) => {
  console.log("Received transcription request");
  
  if (!req.file) {
    console.error("No file received in request");
    return res.status(400).json({ error: "No audio file provided" });
  }

  if (!process.env.GROQ_API_KEY) {
    console.error("GROQ_API_KEY not found in environment");
    return res.status(500).json({ error: "Transcription service configuration error" });
  }

  console.log("Processing file:", req.file.originalname, "Size:", req.file.size);
  
  try {
    const filePath = req.file.path;

    // Verify file type
    const mimeType = req.file.mimetype;
    if (mimeType !== 'audio/webm') {
      console.error('Invalid mime type:', mimeType);
      return res.status(400).json({
        error: "Invalid file format",
        message: "Only audio/webm format is supported"
      });
    }

    console.log("Processing file:", {
      name: req.file.originalname,
      type: req.file.mimetype,
      size: (req.file.size / 1024).toFixed(2) + ' KB'
    });

    const fileStream = fs.createReadStream(filePath);
    const transcription = await groq.audio.transcriptions.create({
      file: fileStream,
      model: "whisper-large-v3-turbo",
      response_format: "verbose_json",
      language: "en"
    });

    console.log("Transcription result received:", {
      success: true,
      textLength: transcription.text?.length || 0
    });

    console.log("Transcription successful");

    // Clean up temp file
    fs.unlinkSync(filePath);

    res.json({
      text: transcription.text,
      success: true
    });
  } catch (err) {
    console.error("Groq transcription error:", err);
    
    // Clean up temp file even if transcription fails
    if (req.file && req.file.path) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkErr) {
        console.error("Error deleting temp file:", unlinkErr);
      }
    }

    const errorMessage = err.message || "Unknown error occurred";
    console.error("Transcription error:", errorMessage);
    
    // Send detailed error response
    res.status(500).json({
      error: "Transcription failed",
      message: errorMessage,
      success: false,
      details: {
        originalError: err.toString(),
        stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
      }
    });
  }
});

module.exports = router;