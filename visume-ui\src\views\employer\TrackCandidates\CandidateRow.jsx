import React from "react";
import { useNavigate } from "react-router-dom";
import {
  HiOutlineVideoCamera,
  HiOutlineDocumentText,
  HiOutlineBriefcase,
  HiLockOpen,
  HiLockClosed,
  HiHeart,
} from "react-icons/hi";
import defaultProfile from "../../../assets/img/default-profile.png";
import ScoreButton from "../components/ScoreButton";

const CandidateRow = ({
  candidate,
  loadingId,
  onStatusChange,
  onToggleUnlock,
  unShortlistCandidate,
  tabName,
}) => {
  const navigate = useNavigate();

  const candidateName =
    candidate.cand_name ||
    (candidate.jobseeker && candidate.jobseeker.cand_name) ||
    "Candidate";

  const profileImg =
    candidate.profile_picture ||
    (candidate.jobseeker && candidate.jobseeker.profile_picture);

  const profileSrc = profileImg
    ? `${import.meta.env.VITE_APP_HOST}/${profileImg}`
    : defaultProfile;

  const scoreData = (() => {
    try {
      const parsed = JSON.parse(candidate?.score || "{}")?.score || {};
      return {
        overallScore: parsed.Overall_Score || 0,
        skillScore: parsed.Skill_Score || 0,
        communicationScore: parsed.Communication_Score || 0,
      };
    } catch {
      return { overallScore: 0, skillScore: 0, communicationScore: 0 };
    }
  })();

  const isUnlocked = tabName.toLowerCase() === "unlocked";

  return (
    <tr className="hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors duration-200">
      <td className="flex items-center p-4">
        <input
          type="checkbox"
          className="mr-3 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
        />
        <div className="flex items-center space-x-4">
          <div className="relative">
            <img
              src={profileSrc}
              alt={`${candidateName} avatar`}
              className="h-12 w-12 rounded-full border-2 border-white shadow-md object-cover"
              onError={(e) => {
                if (e.target.src !== defaultProfile) {
                  e.target.onerror = null;
                  e.target.src = defaultProfile;
                }
              }}
            />
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
          </div>
          <div>
            <h3
              onClick={() =>
                navigate(`/profile/${candidate.video_profile_id}`)
              }
              className="flex cursor-pointer items-center gap-2 font-bold text-gray-900 hover:text-blue-600 dark:text-white dark:hover:text-blue-400 transition-colors duration-200"
            >
              {candidateName}
              <div className="flex gap-1">
                <HiOutlineVideoCamera className="text-blue-500" />
                <HiOutlineDocumentText className="text-green-500" />
              </div>
            </h3>
            <div className="flex items-center space-x-1 mt-1">
              <HiOutlineBriefcase className="text-gray-400 dark:text-gray-500" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {candidate.preferred_location || "N/A"}
              </p>
            </div>
          </div>
        </div>
      </td>
      <td className="p-4">
        <span className="inline-flex items-center gap-1 rounded-full bg-purple-100 px-3 py-1 text-sm font-semibold text-purple-800 border border-purple-200">
          {candidate.role || "Full Stack Developer"}
        </span>
      </td>
      <td className="p-4">
        <div className="flex flex-wrap gap-2">
          {candidate.cand_skills?.split(",").slice(0, 3).map((skill, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 px-3 py-1 text-xs font-semibold text-indigo-700 border border-indigo-200 shadow-sm"
            >
              <svg className="w-2 h-2 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="10" />
              </svg>
              {skill.trim()}
            </span>
          ))}
          {candidate.cand_skills && candidate.cand_skills.split(",").length > 3 && (
            <span className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-gray-100 to-gray-200 px-3 py-1 text-xs font-semibold text-gray-700 border border-gray-200">
              +{candidate.cand_skills.split(",").length - 3} more
            </span>
          )}
        </div>
      </td>
      <td className="p-4">
        {/* Enhanced Score Display */}
        <div className="flex items-center gap-2">
          <ScoreButton {...scoreData} />
        </div>
      </td>
      <td className="p-4">
        <div className="flex items-center gap-2">
          {candidate.unlocked ? (
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {candidate.cand_email}
            </span>
          ) : (
            <span className="text-sm text-gray-500 dark:text-gray-400 font-mono">
              ***@***.com
            </span>
          )}
          <button
            onClick={() => onToggleUnlock(candidate.id)}
            className={`p-1 rounded-full transition-colors duration-200 ${
              candidate.unlocked
                ? "text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20"
                : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
            }`}
          >
            {candidate.unlocked ? <HiLockOpen /> : <HiLockClosed />}
          </button>
        </div>
      </td>
      <td className="p-4">
        <select
          value={
            candidate.status.charAt(0).toUpperCase() +
            candidate.status.slice(1)
          }
          onChange={(e) => onStatusChange(candidate.id, e.target.value)}
          className="rounded-lg border-2 border-gray-200 dark:border-gray-600 px-3 py-2 text-sm font-medium bg-white dark:bg-navy-700 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
        >
          {["Shortlisted", "Unlocked", "Interviewed", "Offers"]
            .filter((status) => {
              const c = candidate.status;
              if (c === "shortlisted") return status === "Shortlisted" || status === "Unlocked";
              if (c === "unlocked") return status !== "Shortlisted";
              if (c === "interviewed") return status === "Interviewed" || status === "Offers";
              if (c === "offers") return status === "Offers";
              return true;
            })
            .map((status) => (
              <option key={status} value={status}>
                {loadingId === candidate.video_profile_id
                  ? "Loading..."
                  : status}
              </option>
            ))}
        </select>
      </td>
      <td className="p-4">
        <div className="flex items-center gap-2">
          <button
            disabled={isUnlocked}
            onClick={() => !isUnlocked && onStatusChange(candidate.id, "Unlocked")}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-200 ${
              !isUnlocked
                ? "bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 hover:from-blue-200 hover:to-indigo-200 hover:text-blue-800 border border-blue-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            }`}
          >
            {!isUnlocked ? (
              <>
                <HiLockOpen className="text-base" />
                <span>Unlock</span>
              </>
            ) : (
              <>
                <HiLockClosed className="text-base" />
                <span>Unlocked</span>
              </>
            )}
          </button>

          {!isUnlocked && (
            <button
              onClick={() =>
                unShortlistCandidate(
                  candidate.video_profile_id,
                  candidate.cand_id
                )
              }
              disabled={loadingId === candidate.cand_id}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-200 ${
                loadingId === candidate.cand_id
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-gradient-to-r from-red-100 to-pink-100 text-red-700 hover:from-red-200 hover:to-pink-200 hover:text-red-800 border border-red-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
              }`}
            >
              <HiHeart className={`text-base ${loadingId === candidate.cand_id ? "text-gray-400" : "text-red-500"}`} />
              {loadingId === candidate.cand_id ? (
                <div className="flex items-center gap-2">
                  <svg
                    className="h-4 w-4 animate-spin text-gray-400"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  <span>Loading...</span>
                </div>
              ) : (
                <span>Remove</span>
              )}
            </button>
          )}
        </div>
      </td>
    </tr>
  );
};

export default CandidateRow;
