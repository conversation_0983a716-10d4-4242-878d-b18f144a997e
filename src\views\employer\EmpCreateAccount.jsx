import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import { API_URL } from '../../config/config';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

const EmpCreateAccount = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    emp_name: '',
    emp_email: '',
    password: '',
    emp_mobile: '',
    designation: '',
  });
  
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Clear error when user types
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };
  
  const validateForm = () => {
    const newErrors = {};
    
    // Validate name
    if (!formData.emp_name.trim()) {
      newErrors.emp_name = 'Full name is required';
    }
    
    // Validate email
    if (!formData.emp_email.trim()) {
      newErrors.emp_email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.emp_email)) {
      newErrors.emp_email = 'Email is invalid';
    }
    
    // Validate password
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    // Validate mobile
    if (!formData.emp_mobile) {
      newErrors.emp_mobile = 'Mobile number is required';
    } else if (!/^\d{10}$/.test(formData.emp_mobile)) {
      newErrors.emp_mobile = 'Mobile number must be 10 digits';
    }
    
    // Validate designation
    if (!formData.designation.trim()) {
      newErrors.designation = 'Designation is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const checkEmailUniqueness = async () => {
    try {
      const response = await axios.post(`${API_URL}/check-employer-email`, { 
        email: formData.emp_email 
      });
      
      if (!response.data.available) {
        setErrors({ ...errors, emp_email: 'Email already exists. Please log in.' });
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error checking email:', error);
      toast.error('Error checking email availability');
      return false;
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    const isEmailUnique = await checkEmailUniqueness();
    if (!isEmailUnique) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await axios.post(`${API_URL}/register-employeer`, {
        emp_name: formData.emp_name,
        emp_email: formData.emp_email,
        password: formData.password,
        emp_mobile: formData.emp_mobile,
        designation: formData.designation,
      });
      
      if (response.data.success) {
        toast.success('Registration successful! Please log in.');
        navigate('/employer/login');
      } else {
        toast.error(response.data.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast.error(error.response?.data?.message || 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="create-account-container">
      <div className="form-card">
        <h2 className="form-title">Create Employer Account</h2>
        <p className="form-subtitle">Fill in your details to get started</p>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="emp_name">Full Name</label>
            <input
              type="text"
              id="emp_name"
              name="emp_name"
              value={formData.emp_name}
              onChange={handleChange}
              placeholder="Enter your full name"
              className={errors.emp_name ? 'error' : ''}
            />
            {errors.emp_name && <span className="error-message">{errors.emp_name}</span>}
          </div>
          
          <div className="form-group">
            <label htmlFor="emp_email">Email Address</label>
            <input
              type="email"
              id="emp_email"
              name="emp_email"
              value={formData.emp_email}
              onChange={handleChange}
              placeholder="Enter your email address"
              className={errors.emp_email ? 'error' : ''}
            />
            {errors.emp_email && <span className="error-message">{errors.emp_email}</span>}
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <div className="password-input-container">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Create a password"
                className={errors.password ? 'error' : ''}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.password && <span className="error-message">{errors.password}</span>}
          </div>
          
          <div className="form-group">
            <label htmlFor="emp_mobile">Mobile Number</label>
            <input
              type="tel"
              id="emp_mobile"
              name="emp_mobile"
              value={formData.emp_mobile}
              onChange={handleChange}
              placeholder="Enter your mobile number"
              className={errors.emp_mobile ? 'error' : ''}
            />
            {errors.emp_mobile && <span className="error-message">{errors.emp_mobile}</span>}
          </div>
          
          <div className="form-group">
            <label htmlFor="designation">Designation</label>
            <input
              type="text"
              id="designation"
              name="designation"
              value={formData.designation}
              onChange={handleChange}
              placeholder="Enter your job title"
              className={errors.designation ? 'error' : ''}
            />
            {errors.designation && <span className="error-message">{errors.designation}</span>}
          </div>
          
          <button 
            type="submit" 
            className="submit-button"
            disabled={isLoading}
          >
            {isLoading ? 'Creating Account...' : 'Create Account'}
          </button>
        </form>
        
        <div className="form-footer">
          <p>Already have an account? <a href="/employer/login">Log In</a></p>
        </div>
      </div>
    </div>
  );
};

export default EmpCreateAccount;