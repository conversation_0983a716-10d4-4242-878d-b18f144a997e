import React from "react";

// Professional StatCard Component
const StatCard = ({ title, value, bgColor, onClick }) => (
  <div 
    className={`${bgColor} rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer w-full`}
    onClick={onClick}
  >
    <div className="flex flex-col">
      <p className="text-sm font-medium text-white/90 mb-1">
        {title}
      </p>
      <p className="text-2xl font-bold text-white">
        {value || 0}
      </p>
    </div>
  </div>
);

const StatsOverview = ({
  shortListedCandidatesCount,
  unlockedCandidatesCount,
  InterviewedCandidatesCount,
  offeredCandidatesCount,
  navigate,
}) => (
  <div className="col-span-full lg:col-span-9">
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
      <StatCard
        title="Shortlisted"
        value={shortListedCandidatesCount}
        bgColor="bg-blue-600"
        onClick={() => navigate("/employer/track-candidates?tab=Shortlisted")}
      />
      <StatCard
        title="Unlocked"
        value={unlockedCandidatesCount}
        bgColor="bg-amber-600"
        onClick={() => navigate("/employer/track-candidates?tab=Unlocked")}
      />
      <StatCard
        title="Interviews"
        value={InterviewedCandidatesCount}
        bgColor="bg-orange-600"
        onClick={() => navigate("/employer/track-candidates?tab=Interview")}
      />
      <StatCard
        title="Offers"
        value={offeredCandidatesCount}
        bgColor="bg-green-600"
        onClick={() => navigate("/employer/track-candidates?tab=Offers")}
      />
    </div>
  </div>
);

export default StatsOverview;