"use client"
import { useState } from "react"
import { motion } from "framer-motion"
import { Menu, X } from "lucide-react"
import WaitlistModal from "./WaitlistModal"

const Navbar = ({ activeView = "jobseeker", setActiveView }) => {
  const [menuOpen, setMenuOpen] = useState(false)
  const [waitlistOpen, setWaitlistOpen] = useState(false)

  const navLinks = ["Features", "How it Works"]

  const openWaitlistModal = () => setWaitlistOpen(true)
  const closeWaitlistModal = () => setWaitlistOpen(false)

  return (
    <>
      <WaitlistModal open={waitlistOpen} onClose={closeWaitlistModal} />
      {/* Google Fonts Import */}
      <link href="https://fonts.googleapis.com/css2?family=Sora:wght@600&display=swap" rel="stylesheet" />
      <div className="fixed top-6 left-0 right-0 z-50 flex justify-center px-4">
        <motion.nav
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="relative"
        >
          <div className="bg-white/90 backdrop-blur-sm rounded-full shadow-sm border border-blue-200/60 pl-6 pr-1 py-1">
            <div className="flex items-center gap-8">
              {/* Logo */}
              <motion.div className="flex items-center space-x-2" whileHover={{ scale: 1.02 }}>
                <img src="/visume-logo.png" alt="Visume Logo" className="h-6 w-6 object-contain" />
                <span className="font-semibold text-lg text-gray-900" style={{ fontFamily: "Sora, sans-serif" }}>
                  Visume
                </span>
              </motion.div>
              {/* Navigation Links (desktop) */}
              <div className="hidden md:flex items-center space-x-8">
                {navLinks.map((item, index) => (
                  <motion.a
                    key={item}
                    href={`#${item.toLowerCase().replace(/ /g, "-")}`}
                    className="text-sm font-semibold text-gray-600 hover:text-gray-900 transition-colors duration-200"
                    style={{ fontFamily: "Sora, sans-serif" }}
                    whileHover={{ y: -1 }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.2 }}
                  >
                    {item}
                  </motion.a>
                ))}
              </div>
              {/* Right side - CTA Button (desktop) */}
              <div className="hidden md:flex items-center">
                <motion.button
                  className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold hover:bg-blue-600 transition-colors duration-200 flex items-center gap-2"
                  style={{ fontFamily: "Sora, sans-serif" }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, x: 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  onClick={openWaitlistModal}
                >
                  <div className="w-4 h-4 bg-white/20 rounded flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-sm"></div>
                  </div>
                  Join Waitlist
                </motion.button>
              </div>
              {/* Mobile menu button */}
              <div className="md:hidden">
                <button
                  aria-label={menuOpen ? "Close menu" : "Open menu"}
                  className="p-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
                  onClick={() => setMenuOpen(!menuOpen)}
                >
                  {menuOpen ? <X className="h-4 w-4 text-gray-900" /> : <Menu className="h-4 w-4 text-gray-900" />}
                </button>
              </div>
            </div>
            {/* Mobile menu */}
            {menuOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-md rounded-2xl shadow-lg border border-blue-200/80 p-4 md:hidden"
              >
                <div className="flex flex-col space-y-3">
                  {navLinks.map((item) => (
                    <a
                      key={item}
                      href={`#${item.toLowerCase().replace(/ /g, "-")}`}
                      className="text-sm font-semibold text-gray-700 hover:text-gray-900 py-2 px-2 rounded-lg hover:bg-gray-100/80 transition-colors duration-200"
                      style={{ fontFamily: "Sora, sans-serif" }}
                      onClick={() => setMenuOpen(false)}
                    >
                      {item}
                    </a>
                  ))}
                  <button
                    className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold hover:bg-blue-600 transition-colors duration-200 w-full flex items-center justify-center gap-2"
                    style={{ fontFamily: "Sora, sans-serif" }}
                    onClick={() => {
                      setMenuOpen(false)
                      openWaitlistModal()
                    }}
                  >
                    <div className="w-4 h-4 bg-white/20 rounded flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-sm"></div>
                    </div>
                    Join Waitlist
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        </motion.nav>
      </div>
    </>
  )
}

export default Navbar
