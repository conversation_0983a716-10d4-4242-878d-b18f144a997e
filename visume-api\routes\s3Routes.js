const express = require("express");
const router = express.Router();
const s3Upload = require("../controllers/s3UploadController");

// Debug middleware for S3 routes
router.use((req, res, next) => {
    console.log('[DEBUG] S3 Route accessed:', req.method, req.path);
    next();
});

// Health check endpoint to validate S3 connectivity
router.get('/s3/health', (req, res, next) => {
    console.log('[DEBUG] S3 health check route hit');
    next();
}, s3Upload.checkS3Health);

router.get(
    "/get-s3-url/:fileName",
    (req, res, next) => {
        console.log('[DEBUG] Get S3 URL route hit for file:', req.params.fileName);
        next();
    },
    s3Upload.getS3Url
);

// ============================================================================
// MULTIPART UPLOAD ROUTES
// ============================================================================

// Initialize multipart upload
router.post('/multipart/init', (req, res, next) => {
    console.log('[DEBUG] Initialize multipart upload route hit');
    next();
}, s3Upload.initializeMultipartUpload);

// Generate pre-signed URL for uploading a specific part
router.get('/multipart/part-url/:uploadId/:partNumber', (req, res, next) => {
    console.log('[DEBUG] Generate part upload URL route hit:', req.params);
    next();
}, s3Upload.generatePartUploadUrl);

// Complete multipart upload
router.post('/multipart/complete', (req, res, next) => {
    console.log('[DEBUG] Complete multipart upload route hit');
    next();
}, s3Upload.completeMultipartUpload);

// Abort multipart upload
router.delete('/multipart/abort/:uploadId', (req, res, next) => {
    console.log('[DEBUG] Abort multipart upload route hit:', req.params.uploadId);
    next();
}, s3Upload.abortMultipartUpload);

// List uploaded parts
router.get('/multipart/parts/:uploadId', (req, res, next) => {
    console.log('[DEBUG] List upload parts route hit:', req.params.uploadId);
    next();
}, s3Upload.listUploadParts);

module.exports = router;
