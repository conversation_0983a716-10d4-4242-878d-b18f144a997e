# Design System Documentation

> **File**: `designsystem.md`  
> **Version**: 1.0  
> **Last Updated**: July 2025  
> **Purpose**: Complete design system specification for development team

## Typography

### Primary Fonts
- **Manrope**: Used for body text, buttons, and general UI elements
- **Sora**: Used for headings, titles, and emphasis text

### Font Hierarchy
```css
/* Headings - Sora */
h1: font-family: 'Sora', sans-serif; font-size: 48px; font-weight: 700; line-height: 1.2;
h2: font-family: 'Sora', sans-serif; font-size: 36px; font-weight: 600; line-height: 1.3;
h3: font-family: 'Sora', sans-serif; font-size: 28px; font-weight: 600; line-height: 1.4;
h4: font-family: 'Sora', sans-serif; font-size: 24px; font-weight: 500; line-height: 1.4;
h5: font-family: 'Sora', sans-serif; font-size: 20px; font-weight: 500; line-height: 1.5;

/* Body Text - Manrope */
body-large: font-family: 'Manrope', sans-serif; font-size: 18px; font-weight: 400; line-height: 1.6;
body-medium: font-family: 'Manrope', sans-serif; font-size: 16px; font-weight: 400; line-height: 1.6;
body-small: font-family: 'Manrope', sans-serif; font-size: 14px; font-weight: 400; line-height: 1.5;
body-xs: font-family: 'Manrope', sans-serif; font-size: 12px; font-weight: 400; line-height: 1.4;

/* UI Elements - Manrope */
button-text: font-family: 'Manrope', sans-serif; font-size: 16px; font-weight: 500;
input-text: font-family: 'Manrope', sans-serif; font-size: 16px; font-weight: 400;
caption: font-family: 'Manrope', sans-serif; font-size: 12px; font-weight: 400;
```

## Color System

### Dark Mode (Primary Theme)
```css
/* Background Colors */
--bg-primary: #0F172A;          /* Main background */
--bg-secondary: #1E293B;        /* Card backgrounds */
--bg-tertiary: #334155;         /* Elevated surfaces */
--bg-accent: #475569;           /* Subtle backgrounds */

/* Text Colors */
--text-primary: #F8FAFC;        /* Primary text */
--text-secondary: #CBD5E1;      /* Secondary text */
--text-muted: #94A3B8;          /* Muted text */
--text-disabled: #64748B;       /* Disabled text */

/* Brand Colors */
--brand-primary: #3B82F6;       /* Primary blue */
--brand-secondary: #6366F1;     /* Secondary purple-blue */
--brand-accent: #8B5CF6;        /* Accent purple */

/* Status Colors */
--success: #10B981;             /* Green for success */
--warning: #F59E0B;             /* Orange for warnings */
--error: #EF4444;               /* Red for errors */
--info: #06B6D4;                /* Cyan for info */

/* Interactive Colors */
--hover: rgba(59, 130, 246, 0.1);
--active: rgba(59, 130, 246, 0.2);
--focus: rgba(59, 130, 246, 0.3);
```

### Light Mode
```css
/* Background Colors */
--bg-primary: #FFFFFF;          /* Main background */
--bg-secondary: #F8FAFC;        /* Card backgrounds */
--bg-tertiary: #F1F5F9;         /* Elevated surfaces */
--bg-accent: #E2E8F0;           /* Subtle backgrounds */

/* Text Colors */
--text-primary: #0F172A;        /* Primary text */
--text-secondary: #475569;      /* Secondary text */
--text-muted: #64748B;          /* Muted text */
--text-disabled: #94A3B8;       /* Disabled text */

/* Brand Colors (same as dark mode) */
--brand-primary: #3B82F6;
--brand-secondary: #6366F1;
--brand-accent: #8B5CF6;

/* Status Colors (same as dark mode) */
--success: #10B981;
--warning: #F59E0B;
--error: #EF4444;
--info: #06B6D4;

/* Interactive Colors */
--hover: rgba(59, 130, 246, 0.05);
--active: rgba(59, 130, 246, 0.1);
--focus: rgba(59, 130, 246, 0.15);
```

## Layout System

### Grid System
```css
/* Container Sizes */
--container-xs: 480px;
--container-sm: 640px;
--container-md: 768px;
--container-lg: 1024px;
--container-xl: 1280px;
--container-2xl: 1536px;

/* Spacing Scale */
--space-1: 4px;
--space-2: 8px;
--space-3: 12px;
--space-4: 16px;
--space-5: 20px;
--space-6: 24px;
--space-8: 32px;
--space-10: 40px;
--space-12: 48px;
--space-16: 64px;
--space-20: 80px;
--space-24: 96px;
--space-32: 128px;
```

### Dashboard Layout
```css
/* Sidebar */
.sidebar {
  width: 280px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--bg-tertiary);
  padding: var(--space-6);
}

/* Main Content */
.main-content {
  margin-left: 280px;
  padding: var(--space-8);
  background: var(--bg-primary);
}

/* Card Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
}
```

## Component Specifications

### Cards
```css
.card {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: var(--space-6);
  border: 1px solid var(--bg-tertiary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: var(--space-4);
}

.card-title {
  font-family: 'Sora', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  font-family: 'Manrope', sans-serif;
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: var(--space-1);
}
```

### Buttons
```css
/* Primary Button */
.btn-primary {
  background: var(--brand-primary);
  color: white;
  border: none;
  border-radius: 12px;
  padding: var(--space-3) var(--space-6);
  font-family: 'Manrope', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #2563EB;
  transform: translateY(-1px);
}

/* Secondary Button */
.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--bg-tertiary);
  border-radius: 12px;
  padding: var(--space-3) var(--space-6);
  font-family: 'Manrope', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--hover);
  border-color: var(--brand-primary);
}

/* Small Button */
.btn-small {
  padding: var(--space-2) var(--space-4);
  font-size: 14px;
  border-radius: 8px;
}
```

### Form Elements
```css
/* Input Fields */
.input {
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-accent);
  border-radius: 8px;
  padding: var(--space-3) var(--space-4);
  font-family: 'Manrope', sans-serif;
  font-size: 16px;
  color: var(--text-primary);
  width: 100%;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 3px var(--focus);
}

.input::placeholder {
  color: var(--text-muted);
}

/* Form Labels */
.label {
  font-family: 'Manrope', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  display: block;
}
```

### Navigation
```css
/* Sidebar Navigation */
.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  border-radius: 8px;
  color: var(--text-secondary);
  text-decoration: none;
  font-family: 'Manrope', sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: var(--space-2);
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: var(--hover);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--brand-primary);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--space-3);
}
```

### Charts and Data Visualization
```css
/* Chart Container */
.chart-container {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: var(--space-6);
  height: 300px;
}

/* Metric Display */
.metric {
  text-align: center;
}

.metric-value {
  font-family: 'Sora', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.metric-label {
  font-family: 'Manrope', sans-serif;
  font-size: 14px;
  color: var(--text-secondary);
}

.metric-change {
  font-family: 'Manrope', sans-serif;
  font-size: 14px;
  font-weight: 500;
  margin-top: var(--space-1);
}

.metric-change.positive {
  color: var(--success);
}

.metric-change.negative {
  color: var(--error);
}
```

### Calendar Component
```css
.calendar {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: var(--space-6);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.calendar-title {
  font-family: 'Sora', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--space-1);
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-family: 'Manrope', sans-serif;
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.calendar-day:hover {
  background: var(--hover);
}

.calendar-day.selected {
  background: var(--brand-primary);
  color: white;
}

.calendar-day.today {
  background: var(--brand-secondary);
  color: white;
}
```

## Interactive States

### Hover Effects
```css
/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease;
}

/* Card hover */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Button hover states already defined above */

/* Link hover */
a:hover {
  color: var(--brand-primary);
}
```

### Loading States
```css
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.skeleton {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-accent) 50%, var(--bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

## Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

### Mobile Adaptations
```css
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    position: fixed;
    z-index: 50;
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
    padding: var(--space-4);
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .metric-value {
    font-size: 24px;
  }
}
```

## Accessibility

### Focus Management
```css
/* Focus styles */
*:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--brand-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}
```

### Screen Reader Support
```css
/* Visually hidden but accessible to screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

## Animation Guidelines

### Micro-interactions
```css
/* Subtle animations for better UX */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}
```

## Implementation Notes

### CSS Custom Properties Usage
- All colors should use CSS custom properties for easy theme switching
- Implement theme toggle functionality using JavaScript to switch between light and dark mode
- Use consistent spacing scale throughout the application
- Maintain consistent border-radius values (8px for small elements, 12px for buttons, 16px for cards)

### Component Architecture
- Build components with consistent padding and margin patterns
- Use flexbox and grid for layouts
- Implement proper semantic HTML structure
- Ensure all interactive elements have proper focus states
- Test components in both light and dark modes

### Performance Considerations
- Optimize font loading with font-display: swap
- Use CSS containment where appropriate
- Minimize layout thrashing with proper CSS transitions
- Implement efficient CSS selectors