// Utility functions for CreateVR API calls

export async function fetchJobRoles() {
  try {
    const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/get-roles`);
    const data = await response.json();
    if (data && Array.isArray(data.roles)) {
      return data.roles.map((role) => role.role_name);
    }
    return [];
  } catch {
    return [];
  }
}

export async function fetchSkills() {
  try {
    const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/get-skills`);
    const data = await response.json();
    if (data && Array.isArray(data.skills)) {
      return data.skills.map((skill) => skill.skill_name);
    }
    return [];
  } catch {
    return [];
  }
}

export async function submitCreateVR(formData) {
  const url = `${import.meta.env.VITE_APP_HOST}/api/v1/create-video-resume`;
  const response = await fetch(url, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(formData),
  });
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message || `HTTP error! status: ${response.status}`);
  }
  if (!data.questions || !Array.isArray(data.questions) || data.questions.length === 0) {
    throw new Error("No questions received from API");
  }
  return data;
}