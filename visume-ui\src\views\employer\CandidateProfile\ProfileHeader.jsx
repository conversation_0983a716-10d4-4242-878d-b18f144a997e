// ProfileHeader.jsx
import React, { useState } from "react";
import {
  MapPin,
  IndianRupee,
  Star,
  MessageCircle,
  BarChart2,
} from "lucide-react";
import Progress from "components/progress";
import toast from "react-hot-toast";

function ScoreCard({ icon, title, score, color }) {
  return (
    <div className="flex min-w-[160px] items-center justify-between rounded-lg border border-gray-100 bg-gradient-to-br from-white to-gray-50 p-2 shadow">
      <div className="flex items-center gap-2">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-lg bg-${color}-50`}
        >
          {icon}
        </div>
        <div>
          <div className="text-sm font-medium text-gray-800">{title}</div>
          <div className="flex items-center gap-1">
            <span className="text-base font-semibold text-gray-900">
              {score}
            </span>
            <span
              className={`inline-block rounded px-1.5 py-0.5 text-[10px] font-normal
              ${
                color === "yellow"
                  ? "bg-yellow-100 text-yellow-700"
                  : color === "blue"
                  ? "bg-blue-100 text-blue-700"
                  : color === "green"
                  ? "bg-green-100 text-green-700"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {score >= 80 ? "Excellent" : score >= 60 ? "Good" : "Average"}
            </span>
          </div>
        </div>
      </div>
      <div className="ml-2 flex h-full flex-col items-center justify-center">
        <Progress value={score} color={color} width="h-16 w-1.5" />
      </div>
    </div>
  );
}

const ProfileHeader = ({
  candidateProf,
  profileData,
  strippedResumeJson,
  questionsAndAnswers, // Added prop
  videoRef, // NEW: video ref for controlling playback
}) => {
  const [shortlistLoading, setShortlistLoading] = useState(false);
  const [shortlisted, setShortlisted] = useState(false);
  const [videoPlaying, setVideoPlaying] = useState(false);

  // Shortlist handler
  const handleShortlist = async () => {
    if (!candidateProf?.cand_id || !candidateProf?.emp_id) {
      toast.error("Candidate ID or Employer ID not found.");
      return;
    }
    setShortlistLoading(true);
    try {
      const emp_id = candidateProf.emp_id;
      const cand_id = candidateProf.video_profile_id;
      console.log(
        "Shortlist API call - emp_id:",
        emp_id,
        "cand_id (video_profile_id):",
        cand_id
      );
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ emp_id, cand_id }),
        }
      );
      const data = await response.json();
      if (!response.ok) {
        toast.error(data?.message || "Failed to shortlist candidate");
        return;
      }
      setShortlisted(true);
      toast.success(data?.message || "Candidate shortlisted!");
    } catch (error) {
      toast.error(error.message || "Failed to shortlist candidate");
    } finally {
      setShortlistLoading(false);
    }
  };

  return (
    <div className="max-w-full space-y-2  p-0">
      <div className="flex flex-col gap-4 rounded-2xl bg-white p-6 shadow">
        <style>
          {`
            .video-overlay {
              position: absolute;
              inset: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              background: rgba(0,0,0,0.25);
              opacity: 0;
              transition: opacity 0.2s;
              cursor: pointer;
              z-index: 2;
            }
            .video-card:hover .video-overlay {
              opacity: 1;
            }
            .video-overlay .play-icon {
              background: rgba(255,255,255,0.8);
              border-radius: 50%;
              padding: 12px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.15);
              display: flex;
              align-items: center;
              justify-content: center;
            }
          `}
        </style>
        <div className="flex flex-col items-center gap-3 md:flex-row md:items-center md:justify-start">
          <div className="flex h-24 w-24 flex-shrink-0 items-center justify-center overflow-hidden rounded-full border-2 border-brand-200 bg-white shadow-sm">
            {candidateProf.profile_picture &&
            candidateProf.profile_picture !== "" ? (
              <img
                src={
                  candidateProf.profile_picture.startsWith("http")
                    ? candidateProf.profile_picture
                    : `${import.meta.env.VITE_APP_HOST}/${
                        candidateProf.profile_picture
                      }`
                }
                alt={candidateProf.cand_name}
                className="h-full w-full object-cover object-center"
                onError={(e) => {
                  e.target.style.display = "none";
                }}
              />
            ) : (
              <svg
                className="h-14 w-14 text-brand-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                />
              </svg>
            )}
          </div>
          <div className="flex flex-col items-center md:items-start">
            <span className="text-2xl font-bold text-gray-900">
              {candidateProf.cand_name}
            </span>
            <span className="mt-1 flex items-center gap-2 text-lg font-semibold text-brand-600">
              <BarChart2 className="h-5 w-5 text-brand-600" />
              {profileData.role}
            </span>
          </div>
        </div>
        {profileData.video_url ? (
          <div
            className="video-card relative mt-4 flex w-full items-center justify-center rounded-xl border border-gray-100 bg-gradient-to-br from-gray-50 via-white to-gray-200 p-4 shadow-lg"
            style={{ position: "relative" }}
          >
            <video
              ref={videoRef}
              controls={true}
              className="h-[400px] w-full rounded-xl object-contain shadow-md"
              src={profileData.video_url}
              poster="/src/assets/img/videores-illustration.png"
              onPlay={() => setVideoPlaying(true)}
              onPause={() => setVideoPlaying(false)}
            />
            {!videoPlaying && (
              <div
                className="video-overlay"
                onClick={() => {
                  if (videoRef?.current) {
                    videoRef.current.play();
                  }
                }}
              >
                <span className="play-icon">
                  <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                    <circle cx="24" cy="24" r="24" fill="#fff" opacity="0.7"/>
                    <polygon points="20,16 36,24 20,32" fill="#333"/>
                  </svg>
                </span>
              </div>
            )}
          </div>
        ) : (
          <div className="mt-4 flex h-[425px] w-full items-center justify-center rounded-xl border border-gray-100 bg-gradient-to-br from-gray-400 via-gray-500 to-gray-700 text-white shadow-lg">
            No Video Url Found
          </div>
        )}
        <div className="mt-8 flex flex-col items-stretch justify-between gap-6 sm:flex-row">
          <button
            className={`flex w-full items-center justify-center gap-2 rounded-xl border border-gray-200 bg-gradient-to-r from-white via-gray-50 to-gray-100 px-6 py-3 text-base font-semibold text-gray-700 shadow-sm transition-all duration-200 hover:scale-[1.015] hover:border-brand-200 hover:bg-gradient-to-r hover:from-brand-50 hover:to-white focus:outline-none focus:ring-2 focus:ring-brand-200 ${
              shortlisted ? "cursor-not-allowed opacity-60" : ""
            }`}
            style={{ maxWidth: "100%", letterSpacing: "0.01em" }}
            onClick={handleShortlist}
            disabled={shortlistLoading || shortlisted}
          >
            <Star className="h-5 w-5 text-yellow-400 drop-shadow-sm" />
            {shortlistLoading
              ? "Shortlisting..."
              : shortlisted
              ? "Shortlisted"
              : "Shortlist"}
          </button>
        </div>
      </div>
    </div>
  );
};

export { ScoreCard };
export default ProfileHeader;
