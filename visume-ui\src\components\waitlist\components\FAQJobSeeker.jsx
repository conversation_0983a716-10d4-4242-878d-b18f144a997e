"use client"
import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Plus, Minus } from "lucide-react"

const FAQJobSeeker = () => {
  const [expandedIndex, setExpandedIndex] = useState(null)

  const faqs = [
    {
      question: "How does Visume help me stand out from other job applicants?",
      answer:
        "Visume lets you showcase your personality, communication skills, and passion through video — things that can't be captured in a traditional resume. Instead of being just another PDF in a pile, you become a real person that employers can connect with.",
    },
    {
      question: "Why do I keep getting rejected before interviews?",
      answer:
        "Most rejections happen because ATS systems filter out resumes based on keywords, or because your resume looks identical to hundreds of others. Visume bypasses these barriers by letting employers see the real you through personalized video responses.",
      highlighted: true,
    },
    {
      question: "Do I need video editing skills or professional equipment?",
      answer:
        "Not at all! You just need a webcam or smartphone. Our AI guides you through job-specific questions, and we handle all the editing, formatting, and polishing automatically. No technical skills required.",
    },
    {
      question: "How long does it take to create my Visume?",
      answer:
        "Most job seekers complete their Visume in just 5-10 minutes. You answer a few AI-generated questions tailored to your target role, and we create a professional 2-3 minute video resume instantly.",
    },
    {
      question: "Can I create different Visumes for different types of jobs?",
      answer:
        "You can create multiple Visumes tailored to different roles, industries, or companies. Each one will have job-specific questions that highlight the most relevant aspects of your background and skills.",
    },
    {
      question: "What if I'm not comfortable on camera?",
      answer:
        "Our AI interview process is designed to make you feel comfortable and confident. You can practice as many times as you want, and we provide tips and guidance throughout. Most users find it much easier than they expected!",
    },
  ]

  return (
    <section className="py-6 lg:py-8 bg-white relative">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="grid gap-12 lg:grid-cols-3">
          {/* Left Column - FAQ Header */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm"
              style={{ fontFamily: "Sora, sans-serif" }}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              FAQ
            </motion.div>
            <h2
              className="text-slate-900 mb-6 text-3xl font-bold leading-tight sm:text-4xl lg:text-5xl"
              style={{ fontFamily: "Manrope, sans-serif" }}
            >
              Everything you need to{" "}
              <span className="text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text">know</span>
            </h2>
            <p className="text-slate-600 leading-relaxed text-lg" style={{ fontFamily: "Sora, sans-serif" }}>
              Ready to transform your job search? Get answers to common questions about creating your video resume.
            </p>
          </motion.div>
          {/* Right Column - FAQ Items */}
          <div className="space-y-4 lg:col-span-2">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                className={`group relative overflow-hidden rounded-2xl border transition-all duration-500 ${
                  faq.highlighted
                    ? "border-blue-300/60 bg-gradient-to-br from-blue-50/80 to-indigo-50/40 shadow-xl shadow-blue-200/25"
                    : "border-blue-200/40 bg-white shadow-lg shadow-blue-200/20 hover:border-blue-300/60 hover:shadow-xl hover:shadow-blue-300/25"
                }`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                whileHover={{ y: -2 }}
              >
                <motion.button
                  className="flex w-full items-center justify-between px-6 py-6 text-left transition-all duration-200"
                  onClick={() => setExpandedIndex(expandedIndex === index ? null : index)}
                  whileTap={{ scale: 0.995 }}
                >
                  <h3
                    className="pr-8 text-lg font-bold text-slate-900 leading-tight group-hover:text-blue-700 transition-colors duration-300"
                    style={{ fontFamily: "Manrope, sans-serif" }}
                  >
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{
                      rotate: expandedIndex === index ? 45 : 0,
                    }}
                    transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                    className="flex-shrink-0"
                  >
                    {expandedIndex === index ? (
                      <Minus className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Plus className="h-5 w-5 text-blue-600" />
                    )}
                  </motion.div>
                </motion.button>
                <AnimatePresence>
                  {expandedIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.4, ease: "easeInOut" }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6 pt-2">
                        <motion.div
                          initial={{ y: -10, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ delay: 0.1, duration: 0.3 }}
                          className="border-t border-slate-200/60 pt-4"
                        >
                          <p
                            className="text-slate-600 leading-relaxed text-base"
                            style={{ fontFamily: "Sora, sans-serif" }}
                          >
                            {faq.answer}
                          </p>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
                {/* Subtle Gradient Overlay */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-50/10 via-transparent to-indigo-50/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100 pointer-events-none"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
      {/* Enhanced Custom Styles */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
      `}</style>
    </section>
  )
}

export default FAQJobSeeker
