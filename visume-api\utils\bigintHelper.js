// Helper function to convert BigInt values to Numbers for JSON serialization
// This is needed because Prisma returns BigInt for auto-incrementing IDs
// which cannot be serialized to JSON by default

/**
 * Recursively converts BigInt values to Numbers in an object or array
 * @param {any} obj - The object, array, or primitive value to convert
 * @returns {any} - The converted object with BigInt values as Numbers
 */
const convertBigIntToNumber = (obj) => {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => convertBigIntToNumber(item));
  }
  
  // Handle primitive BigInt
  if (typeof obj === 'bigint') {
    return Number(obj);
  }
  
  // Handle Date objects
  if (obj instanceof Date) {
    return obj;
  }
  
  // Handle objects
  if (typeof obj === 'object') {
    const converted = {};
    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertBigIntToNumber(value);
    }
    return converted;
  }
  
  // Return primitive values as-is
  return obj;
};

module.exports = {
  convertBigIntToNumber
};
