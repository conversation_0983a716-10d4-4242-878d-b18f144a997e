// visume-api/routes/geminiRoutes.js
const express = require("express");
const router = express.Router();
const { GoogleGenerativeAI } = require("@google/generative-ai");


router.post("/assist", async (req, res) => {
  try {
    const { messages, context } = req.body;
    if (!Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: "Messages array is required" });
    }

    // Build context information string
    let contextString = "";
    if (context) {
      contextString += "=== CANDIDATE CONTEXT INFORMATION ===\n\n";
      
      // Candidate basic info
      if (context.candidateProf) {
        contextString += "CANDIDATE PROFILE:\n";
        contextString += `Name: ${context.candidateProf.cand_name || context.candidateProf.name || "Not provided"}\n`;
        contextString += `Email: ${context.candidateProf.cand_email || context.candidateProf.email || "Not provided"}\n`;
        contextString += `Phone: ${context.candidateProf.cand_mobile || context.candidateProf.phone || "Not provided"}\n`;
        contextString += `Location: ${context.candidateProf.preferred_location || context.candidateProf.location || "Not provided"}\n`;
        if (context.candidateProf.current_role) {
          contextString += `Current Role: ${context.candidateProf.current_role}\n`;
        }
        if (context.candidateProf.experience_years) {
          contextString += `Experience: ${context.candidateProf.experience_years} years\n`;
        }
        contextString += "\n";
      }

      // Profile data from video resume
      if (context.profileData) {
        contextString += "VIDEO PROFILE DATA:\n";
        if (context.profileData.current_role) {
          contextString += `Role: ${context.profileData.current_role}\n`;
        }
        if (context.profileData.experience_years) {
          contextString += `Experience: ${context.profileData.experience_years} years\n`;
        }
        if (context.profileData.skills && Array.isArray(context.profileData.skills)) {
          contextString += `Skills: ${context.profileData.skills.join(", ")}\n`;
        }
        if (context.profileData.ai_scores) {
          contextString += `AI Scores: ${JSON.stringify(context.profileData.ai_scores)}\n`;
        }
        contextString += "\n";
      }

      // Resume data
      if (context.strippedResumeJson) {
        contextString += "RESUME INFORMATION:\n";
        
        if (context.strippedResumeJson.summary) {
          contextString += `Summary: ${context.strippedResumeJson.summary}\n\n`;
        }

        if (context.strippedResumeJson.personal_info) {
          const info = context.strippedResumeJson.personal_info;
          if (info.location) contextString += `Current Location: ${info.location}\n`;
          if (info.preferred_location) contextString += `Preferred Location: ${info.preferred_location}\n`;
          if (info.salary_expectation) contextString += `Salary Expectation: ${info.salary_expectation}\n`;
        }

        if (context.strippedResumeJson.skills) {
          if (Array.isArray(context.strippedResumeJson.skills.all_skills)) {
            contextString += `Skills: ${context.strippedResumeJson.skills.all_skills.join(", ")}\n`;
          } else if (Array.isArray(context.strippedResumeJson.skills)) {
            contextString += `Skills: ${context.strippedResumeJson.skills.join(", ")}\n`;
          }
        }

        if (context.strippedResumeJson.experience && Array.isArray(context.strippedResumeJson.experience)) {
          contextString += "\nEXPERIENCE:\n";
          context.strippedResumeJson.experience.forEach((exp, i) => {
            contextString += `${i + 1}. ${exp.title || exp.position || "Role"} at ${exp.company || "Company"}\n`;
            if (exp.duration) contextString += `   Duration: ${exp.duration}\n`;
            if (exp.description) contextString += `   Description: ${exp.description}\n`;
          });
        }

        if (context.strippedResumeJson.education && Array.isArray(context.strippedResumeJson.education)) {
          contextString += "\nEDUCATION:\n";
          context.strippedResumeJson.education.forEach((edu, i) => {
            contextString += `${i + 1}. ${edu.degree || "Degree"} from ${edu.institution || "Institution"}`;
            if (edu.year) contextString += ` (${edu.year})`;
            contextString += "\n";
          });
        }
        contextString += "\n";
      }

      // Video interview Q&A
      if (context.questionsAndAnswers && Array.isArray(context.questionsAndAnswers) && context.questionsAndAnswers.length > 0) {
        contextString += "VIDEO INTERVIEW Q&A:\n";
        context.questionsAndAnswers.forEach((qa, i) => {
          contextString += `Q${i + 1}: ${qa.question}\n`;
          contextString += `A${i + 1}: ${qa.answer}\n\n`;
        });
      }

      contextString += "=== END CONTEXT ===\n\n";
    }

    // Convert messages to Gemini SDK format and prepend context to the first user message
    const geminiMessages = messages.map((msg, index) => {
      if (index === 0 && msg.role === "user" && contextString) {
        return {
          role: msg.role,
          parts: [{ text: contextString + "USER QUESTION: " + msg.text }],
        };
      }
      return {
        role: msg.role,
        parts: [{ text: msg.text }],
      };
    });

    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    
    // Add system instruction for better context understanding
    const result = await model.generateContent({ 
      contents: [
        {
          role: "user",
          parts: [{ 
            text: `You are an expert recruiter AI assistant helping an employer evaluate this job candidate. Provide CONCISE, actionable insights using the candidate's actual data.

Response guidelines:
- Keep answers brief (2-4 sentences max)
- Lead with key strengths/concerns
- Reference specific skills, experience, or interview responses
- End with clear hiring recommendation (Strong fit/Consider/Pass)
- Be direct and professional

Focus on what matters most for hiring decisions.`
          }]
        },
        ...geminiMessages
      ]
    });
    
    const text =
      result?.response?.candidates?.[0]?.content?.parts?.[0]?.text || "";
    res.json({
      candidates: [
        {
          content: {
            parts: [{ text }],
          },
        },
      ],
    });
  } catch (err) {
    console.error("Gemini SDK error:", err.message, err?.response?.data);
    res.status(500).json({ error: "Gemini SDK error", details: err?.response?.data || err.message });
  }
});
// Generate professional summary for a candidate using Gemini
router.post("/generate-summary", async (req, res) => {
  try {
    const { cand_id } = req.body;
    if (!cand_id) {
      return res.status(400).json({ error: "cand_id is required" });
    }

    // Dynamically import Prisma client
    const { PrismaClient } = require("@prisma/client");
    const prisma = new PrismaClient();

    // Fetch candidate profile and stripped_resume
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id: cand_id },
      select: {
        cand_id: true,
        cand_name: true,
        cand_email: true,
        cand_mobile: true,
        preferred_location: true,
        profile_picture: true,
        stripped_resume: true,
      },
    });

    if (!candidate) {
      return res.status(404).json({ error: "Candidate not found" });
    }

    // Prepare prompt for Gemini
    let resumeText = "";
    if (candidate.stripped_resume) {
      try {
        const parsed = typeof candidate.stripped_resume === "string"
          ? JSON.parse(candidate.stripped_resume)
          : candidate.stripped_resume;
        resumeText = JSON.stringify(parsed, null, 2);
      } catch {
        resumeText = candidate.stripped_resume;
      }
    }

    const prompt = `
You are an expert resume writer. Write a concise, professional summary for the following candidate based on their resume and profile details. Use 3-5 sentences, highlight key skills, experience, and strengths, and avoid generic statements.

Candidate Name: ${candidate.cand_name}
Preferred Location: ${candidate.preferred_location}
Skills: ${candidate.skills}
Experience: ${candidate.experience}
Education: ${candidate.education}
Resume Data: ${resumeText}
`;

    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [{ text: prompt }],
        },
      ],
    });
    const summary =
      result?.response?.candidates?.[0]?.content?.parts?.[0]?.text || "";

    res.json({ summary });
  } catch (err) {
    console.error("Gemini summary error:", err.message, err?.response?.data);
    res
      .status(500)
      .json({ error: "Gemini summary error", details: err?.response?.data || err.message });
  }
});

module.exports = router;