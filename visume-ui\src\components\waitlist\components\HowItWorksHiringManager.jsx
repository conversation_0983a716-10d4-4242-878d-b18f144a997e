"use client"
import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"

const HowItWorksHiringManager = () => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkIfMobile()
    window.addEventListener("resize", checkIfMobile)
    return () => window.removeEventListener("resize", checkIfMobile)
  }, [])

  const steps = [
    {
      step: 1,
      title: "Upload Job Description",
      description:
        "Upload a JD and get AI-matched video interviews. Create detailed job descriptions that attract the right talent to your pool.",
      icon: (
        <img
          src="/forHiringManagers/createjob.png"
          alt="Post Job Description"
          className="drop-shadow-blue h-full w-full object-contain"
        />
      ),
    },
    {
      step: 2,
      title: "Browse Video Interviews",
      description:
        "See communication, confidence, and problem-solving up front. Browse authentic video introductions that reveal cultural fit.",
      icon: (
        <img
          src="/forHiringManagers/recievevideoapplications.png"
          alt="Browse Video Resumes"
          className="drop-shadow-blue h-full w-full object-contain"
        />
      ),
    },
    {
      step: 3,
      title: "No More Guesswork",
      description:
        "No more guesswork from bullet-point resumes. Quickly assess real communication skills and candidate enthusiasm.",
      icon: (
        <img
          src="/forHiringManagers/screening.png"
          alt="Screen Efficiently"
          className="drop-shadow-blue h-full w-full object-contain"
        />
      ),
    },
    {
      step: 4,
      title: "Hire Faster",
      description:
        "Hire faster with pre-screened, high-context profiles. Interview qualified candidates who demonstrate genuine interest and fit.",
      icon: (
        <img
          src="/forHiringManagers/hirebetter.png"
          alt="Make Better Hires"
          className="drop-shadow-blue h-full w-full object-contain"
        />
      ),
    },
  ]

  return (
    <section className="py-6 lg:py-8 bg-white relative" id="how-it-works">
      <div className="relative mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <motion.div
          className="mb-12 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm"
            style={{ fontFamily: "Sora, sans-serif" }}
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            How It Works
          </motion.div>
          <h2
            className="text-slate-900 mb-6 text-3xl font-bold leading-tight sm:text-4xl lg:text-5xl"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            Streamline Your
            <span className="text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text">
              {" "}
              Hiring Process
            </span>
          </h2>
          <p
            className="text-slate-600 mx-auto max-w-3xl text-lg leading-relaxed"
            style={{ fontFamily: "Sora, sans-serif" }}
          >
            Discover exceptional talent through video resumes that reveal personality, skills, and cultural fit beyond
            traditional CVs. Make confident hiring decisions faster.
          </p>
        </motion.div>

        {/* Enhanced Mobile Scroll Indicator */}
        {isMobile && (
          <motion.div
            className="text-slate-500 border-slate-200/60 mx-auto mb-6 flex w-fit items-center justify-center rounded-full border bg-white/60 backdrop-blur-sm px-4 py-2.5 text-sm font-medium shadow-sm"
            style={{ fontFamily: "Sora, sans-serif" }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="mx-3">Swipe to explore steps</span>
            <ChevronRight className="h-4 w-4" />
          </motion.div>
        )}

        {/* Steps Content */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className={`${
            isMobile
              ? "scrollbar-hide flex snap-x snap-mandatory space-x-5 overflow-x-auto pb-6"
              : "grid gap-6 md:grid-cols-2 lg:grid-cols-4"
          }`}
        >
          {steps.map((step, index) => (
            <motion.div
              key={step.step}
              className={`relative ${isMobile ? "w-80 flex-none snap-center" : ""} group`}
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                delay: index * 0.15,
                duration: 0.8,
                ease: "easeOut",
              }}
            >
              {/* Modern Connecting Line for Desktop */}
              {!isMobile && index < steps.length - 1 && (
                <motion.div
                  className="absolute left-1/2 top-1/2 z-0 hidden h-px w-full bg-gradient-to-r from-transparent via-blue-300/60 to-transparent lg:block"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ delay: index * 0.15 + 0.5, duration: 0.8 }}
                />
              )}

              {/* Step Number Badge */}
              <div className="absolute -top-3 left-6 z-20 flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 text-sm font-bold text-white shadow-lg">
                {step.step}
              </div>

              {/* Card with new layout structure */}
              <div className="relative h-full rounded-2xl bg-white border border-slate-200/60 shadow-lg shadow-slate-200/20 transition-all duration-500 group-hover:-translate-y-3 hover:shadow-xl hover:shadow-slate-300/25 hover:border-slate-300/80 overflow-hidden">
                {/* Visual Area - Top 70% */}
                <div className="relative h-48 bg-gradient-to-br from-slate-50 to-slate-100/50 flex items-center justify-center p-4">
                  <motion.div
                    className="flex h-full w-full items-center justify-center"
                    whileHover={{ scale: 1.05 }}
                    transition={{
                      type: "spring",
                      stiffness: 400,
                      damping: 15,
                    }}
                  >
                    <div className="h-full w-full transition-all duration-300 group-hover:scale-110 max-h-36 flex items-center justify-center">
                      {step.icon}
                    </div>
                  </motion.div>
                </div>

                {/* Content Area - Bottom 30% */}
                <div className="p-6 bg-white">
                  <h3
                    className="text-slate-900 mb-3 text-lg font-bold leading-tight transition-colors duration-300 group-hover:text-blue-700"
                    style={{ fontFamily: "Manrope, sans-serif" }}
                  >
                    {step.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed" style={{ fontFamily: "Sora, sans-serif" }}>
                    {step.description}
                  </p>
                </div>

                {/* Subtle Gradient Overlay */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-50/10 via-transparent to-indigo-50/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Enhanced Custom Styles */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
        
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .drop-shadow-blue {
          filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.15));
        }
        .drop-shadow-blue-lg {
          filter: drop-shadow(0 8px 20px rgba(59, 130, 246, 0.25));
        }
        @media (max-width: 768px) {
          .snap-x {
            scroll-snap-type: x mandatory;
          }
          .snap-center {
            scroll-snap-align: center;
          }
        }
      `}</style>
    </section>
  )
}

export default HowItWorksHiringManager
