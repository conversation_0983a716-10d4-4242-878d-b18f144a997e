import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import api from "../../../api/axios";

const WaitlistModal = ({ open, onClose, onWaitlisted }) => {
  const [role, setRole] = useState(null);
  const [form, setForm] = useState({
    email: "",
    phone: "",
    company: "",
    industry: "",
  });
  const [touched, setTouched] = useState({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(null);
  const [error, setError] = useState(null);

  // Check if email is already waitlisted
  const checkEmailExists = async (email) => {
    try {
      const res = await api.post("/api/waitlist/check-email", { email });
      return res.data.exists;
    } catch (err) {
      // If error, assume not exists to allow fallback
      return false;
    }
  };
  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
    setTouched({ ...touched, [e.target.name]: true });
  };

  const reset = () => {
    setRole(null);
    setForm({ email: "", phone: "", company: "", industry: "" });
    setTouched({});
    setSuccess(null);
    setError(null);
    setLoading(false);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const isEmailValid = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  const isPhoneValid = (phone) => phone.trim().length > 0;

  const isJobSeekerValid = () => isEmailValid(form.email) && isPhoneValid(form.phone);
  const isHiringValid = () => isEmailValid(form.email) && isPhoneValid(form.phone) && form.company.trim();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setSuccess(null);
    setError(null);

    // Check if email is already waitlisted
    const exists = await checkEmailExists(form.email);
    if (exists) {
      setError("This email is already on the waitlist.");
      setLoading(false);
      return;
    }

    try {
      await api.post("/api/waitlist", {
        userType: role,
        email: form.email,
        phone: form.phone,
        company: form.company,
        industry: form.industry,
      });
      setSuccess("Successfully joined the waitlist!");
      setLoading(false);
      if (onWaitlisted) {
        onWaitlisted();
      }
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      setError(
        err?.response?.data?.message ||
          "Failed to join waitlist. Please try again."
      );
      setLoading(false);
    }
  };

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          className="bg-black/40 fixed inset-0 z-50 flex items-center justify-center overflow-y-auto p-4 backdrop-blur-md"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          {/* Styles moved to global CSS */}

          <motion.div
            className="relative mx-auto max-h-[90vh] w-full max-w-[95vw] p-2 sm:max-w-md sm:p-4 md:max-w-lg md:p-0"
            initial={{ scale: 0.8, opacity: 0, y: 50 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 50 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 25,
              duration: 0.4,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <motion.button
              onClick={handleClose}
              className="group absolute -right-3 -top-3 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-blue-200 bg-white shadow-lg transition-all duration-300 hover:shadow-xl"
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
            >
              <svg
                className="h-5 w-5 text-gray-600 transition-colors group-hover:text-gray-800"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </motion.button>

            {/* Main modal content */}
            <div className="overflow-hidden rounded-2xl border border-blue-200 bg-white shadow-2xl md:rounded-3xl">
              {/* Header */}
              <div className="relative border-b border-blue-100 bg-white p-8 text-center">
                <div className="relative z-10">
                  <motion.h2
                    className="font-sora mb-2 text-3xl font-bold text-blue-900"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    Join the Waitlist
                  </motion.h2>
                  <motion.p
                    className="font-inter text-blue-700"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    Be the first to know when we launch
                  </motion.p>
                </div>
              </div>

              {/* Content area */}
              <div className="p-4 sm:p-6 md:p-8">
                {success ? (
                  <motion.div
                    className="py-8 text-center"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ type: "spring", stiffness: 200 }}
                  >
                    <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
                      <svg
                        className="h-10 w-10 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <h3 className="font-sora mb-2 text-2xl font-bold text-gray-800">
                      Welcome aboard! 🎉
                    </h3>
                    <p className="font-inter text-gray-600">{success}</p>
                  </motion.div>
                ) : (
                  <>
                    {!role ? (
                      <motion.div
                        className="space-y-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        <div className="mb-8 text-center">
                          <h3 className="font-sora mb-2 text-xl font-semibold text-gray-800">
                            Choose your path
                          </h3>
                          <p className="font-inter text-gray-600">
                            Tell us how you'd like to use our platform
                          </p>
                        </div>

                        <motion.button
                          className="font-inter group relative w-full rounded-2xl border border-blue-200 bg-white p-6 text-lg font-semibold text-blue-700 shadow transition-all hover:border-blue-400 hover:bg-blue-50"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setRole("JOB_SEEKER")}
                        >
                          <div className="relative flex items-center justify-center space-x-3">
                            <svg
                              className="h-6 w-6"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                            <span>I'm looking for opportunities</span>
                          </div>
                        </motion.button>

                        <motion.button
                          className="font-inter border-emerald-200 text-emerald-700 hover:border-emerald-400 hover:bg-emerald-50 group relative w-full rounded-2xl border bg-white p-6 text-lg font-semibold shadow transition-all"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setRole("HIRING")}
                        >
                          <div className="relative flex items-center justify-center space-x-3">
                            <svg
                              className="h-6 w-6"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                              />
                            </svg>
                            <span>I'm looking to hire talent</span>
                          </div>
                        </motion.button>
                      </motion.div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        {/* Role selector pills */}
                        <div className="mb-8 flex gap-3 rounded-2xl bg-gray-100 p-1">
                          <button
                            type="button"
                            className={`font-inter flex-1 rounded-xl px-4 py-3 font-semibold transition-all duration-300 ${
                              role === "JOB_SEEKER"
                                ? "scale-105 transform bg-white text-blue-600 shadow-md"
                                : "text-gray-600 hover:text-gray-800"
                            }`}
                            onClick={() => setRole("JOB_SEEKER")}
                          >
                            Job Seeker
                          </button>
                          <button
                            type="button"
                            className={`font-inter flex-1 rounded-xl px-4 py-3 font-semibold transition-all duration-300 ${
                              role === "HIRING"
                                ? "text-emerald-600 scale-105 transform bg-white shadow-md"
                                : "text-gray-600 hover:text-gray-800"
                            }`}
                            onClick={() => setRole("HIRING")}
                          >
                            Hiring
                          </button>
                        </div>

                        <form className="space-y-6" onSubmit={handleSubmit}>
                          {/* Email field */}
                          <div className="space-y-2">
                            <label className="font-inter flex items-center text-sm font-semibold text-gray-700">
                              <svg
                                className="mr-2 h-4 w-4 text-gray-500"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                                />
                              </svg>
                              Email Address
                              <span className="ml-1 text-red-500">*</span>
                            </label>
                            <input
                              type="email"
                              name="email"
                              value={form.email}
                              onChange={handleChange}
                              className="input-focus font-inter w-full rounded-2xl border-2 border-gray-200 bg-white px-4 py-4 text-gray-800 placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/20"
                              required
                              placeholder="<EMAIL>"
                            />
                            {touched.email && !isEmailValid(form.email) && (
                              <motion.p
                                className="font-inter flex items-center text-sm text-red-500"
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                              >
                                <svg
                                  className="mr-1 h-4 w-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                                Please enter a valid email address
                              </motion.p>
                            )}
                          </div>

                          {/* Phone field */}
                          <div className="space-y-2">
                            <label className="font-inter flex items-center text-sm font-semibold text-gray-700">
                              <svg
                                className="mr-2 h-4 w-4 text-gray-500"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                />
                              </svg>
                              Phone Number
                              <span className="ml-1 text-red-500">*</span>
                            </label>
                            <input
                              type="tel"
                              name="phone"
                              value={form.phone}
                              onChange={handleChange}
                              className="input-focus font-inter w-full rounded-2xl border-2 border-gray-200 bg-white px-4 py-4 text-gray-800 placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/20"
                              required
                              placeholder="+****************"
                            />
                            {touched.phone && !isPhoneValid(form.phone) && (
                              <motion.p
                                className="font-inter flex items-center text-sm text-red-500"
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                              >
                                <svg
                                  className="mr-1 h-4 w-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                                Phone number is required
                              </motion.p>
                            )}
                          </div>

                          {/* Hiring-specific fields */}
                          {role === "HIRING" && (
                            <>
                              <div className="space-y-2">
                                <label className="font-inter flex items-center text-sm font-semibold text-gray-700">
                                  <svg
                                    className="mr-2 h-4 w-4 text-gray-500"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                                    />
                                  </svg>
                                  Company Name
                                  <span className="ml-1 text-red-500">*</span>
                                </label>
                                <input
                                  type="text"
                                  name="company"
                                  value={form.company}
                                  onChange={handleChange}
                                  className="focus:border-emerald-500 focus:ring-emerald-500/20 input-focus font-inter w-full rounded-2xl border-2 border-gray-200 bg-white px-4 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-4"
                                  required
                                  placeholder="Your company name"
                                />
                                {touched.company && !form.company.trim() && (
                                  <motion.p
                                    className="font-inter flex items-center text-sm text-red-500"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                  >
                                    <svg
                                      className="mr-1 h-4 w-4"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                      />
                                    </svg>
                                    Company name is required
                                  </motion.p>
                                )}
                              </div>

                              <div className="space-y-2">
                                <label className="font-inter flex items-center text-sm font-semibold text-gray-700">
                                  <svg
                                    className="mr-2 h-4 w-4 text-gray-500"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    />
                                  </svg>
                                  Industry
                                  <span className="ml-2 text-xs text-gray-400">
                                    (optional)
                                  </span>
                                </label>
                                <input
                                  type="text"
                                  name="industry"
                                  value={form.industry}
                                  onChange={handleChange}
                                  className="focus:border-emerald-500 focus:ring-emerald-500/20 input-focus font-inter w-full rounded-2xl border-2 border-gray-200 bg-white px-4 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-4"
                                  placeholder="e.g., Technology, Healthcare, Finance"
                                />
                              </div>
                            </>
                          )}

                          {/* Info text for job seekers */}
                          {role === "JOB_SEEKER" && (
                            <motion.div
                              className="rounded-2xl border border-blue-200 bg-blue-50 p-4"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                            >
                              <div className="flex items-start space-x-3">
                                <svg
                                  className="mt-0.5 h-5 w-5 flex-shrink-0 text-blue-500"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                                <div className="font-inter text-sm text-blue-700">
                                  <p className="mb-1 font-semibold">
                                    We respect your inbox
                                  </p>
                                  <p>
                                    We'll only send you important updates about
                                    our launch. No spam, ever.
                                  </p>
                                </div>
                              </div>
                            </motion.div>
                          )}

                          {/* Error message */}
                          {error && (
                            <motion.div
                              className="flex items-center space-x-3 rounded-2xl border border-red-200 bg-red-50 p-4"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                            >
                              <svg
                                className="h-5 w-5 flex-shrink-0 text-red-500"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              <p className="font-inter text-sm font-medium text-red-700">
                                {error}
                              </p>
                            </motion.div>
                          )}

                          {/* Submit button */}
                          <motion.button
                            type="submit"
                            className={`font-inter relative w-full rounded-2xl px-6 py-4 text-lg font-bold shadow ${
                              role === "JOB_SEEKER"
                                ? "bg-blue-600 text-white hover:bg-blue-700"
                                : "border-emerald-300 text-emerald-700 hover:border-emerald-400 hover:bg-emerald-50 border bg-white"
                            } transition-all`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            disabled={
                              loading ||
                              (role === "JOB_SEEKER" && !isJobSeekerValid()) ||
                              (role === "HIRING" && !isHiringValid())
                            }
                          >
                            <div className="relative flex items-center justify-center space-x-2">
                              {loading ? (
                                <>
                                  <svg
                                    className="h-5 w-5 animate-spin"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                  >
                                    <circle
                                      className="opacity-25"
                                      cx="12"
                                      cy="12"
                                      r="10"
                                      stroke="currentColor"
                                      strokeWidth="4"
                                    ></circle>
                                    <path
                                      className="opacity-75"
                                      fill="currentColor"
                                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                  </svg>
                                  <span>Joining waitlist...</span>
                                </>
                              ) : (
                                <>
                                  <span>Join the Waitlist</span>
                                  <svg
                                    className="h-5 w-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                                    />
                                  </svg>
                                </>
                              )}
                            </div>
                          </motion.button>
                        </form>
                      </motion.div>
                    )}
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WaitlistModal;
