import React, { useEffect, useState } from "react";
import Dropdown from "components/dropdown";
import { FiAlignJustify } from "react-icons/fi";
import { Link, Navigate, useLocation } from "react-router-dom";
import navbarimage from "assets/img/layout/Navbar.png";
import { BsArrowBarUp } from "react-icons/bs";
import { FiSearch } from "react-icons/fi";
import { RiMoonFill, RiSunFill } from "react-icons/ri";
import {
  IoMdNotificationsOutline,
  IoMdInformationCircleOutline,
} from "react-icons/io";
import avatar from "assets/img/avatars/avatar4.png";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";

const Navbar = (props) => {
  const jstoken = Cookies.get("jstoken");
  const candId = Cookies.get("candId");
  const location = useLocation()
  const pathname = location.pathname
  console.log(pathname)
  const [candData, setCandData] = useState({
    name: Cookies.get("role"),
    profileImg: avatar,
  });

  const navigate = useNavigate();
  const deletecookies = () => {
    Cookies.remove("jstoken");
    Cookies.remove("role");
    Cookies.remove("candId");
    Cookies.remove("formData");
    Cookies.remove("questions");
    Cookies.remove("videoProfileId");
    Cookies.remove("skills");
    Cookies.remove("jobRole");
    navigate("/candidate/signIn");
  };

  useEffect(() => {
    const role = Cookies.get("role");
    const empId = Cookies.get("employerId");
    const fetchEmployerInfo = async () => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: empId,
            },
          }
        );
        if (response.ok) {
          const data = await response.json();
          setCandData({
            name: data.data?.emp_name || "Employer",
            profileImg: data.data?.company_logo
              ? (data.data.company_logo.startsWith("http")
                  ? data.data.company_logo
                  : `${import.meta.env.VITE_APP_HOST}/${data.data.company_logo}`)
              : data.data?.profile_picture
              ? (data.data.profile_picture.startsWith("http")
                  ? data.data.profile_picture
                  : `${import.meta.env.VITE_APP_HOST}/${data.data.profile_picture}`)
              : avatar,
          });
        } else {
          console.error(
            "Error fetching employer profile:",
            response.status,
            response.statusText
          );
        }
      } catch (error) {
        console.error("Network error:", error);
      }
    };

    const fetchCandidateInfo = async () => {
      try {
        // Make the API request with the dynamic candId
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (response.ok) {
          const data = await response.json();
          setCandData({
            name: data.candidateProfile[0].cand_name,
            profileImg: data.candidateProfile[0].profile_picture
              ? (data.candidateProfile[0].profile_picture.startsWith("http")
                  ? data.candidateProfile[0].profile_picture
                  : `${import.meta.env.VITE_APP_HOST}/${data.candidateProfile[0].profile_picture}`)
              : avatar,
          });
        } else {
          console.error(
            "Error fetching video profiles:",
            response.status,
            response.statusText
          );
        }
      } catch (error) {
        console.error("Network error:", error);
      }
    };

    // Fetch based on role
    if (role === "employer" && empId) {
      fetchEmployerInfo();
    } else if (candId) {
      fetchCandidateInfo();
    }
  }, []);

  // Listen for company logo updates (employer only)
  useEffect(() => {
    const role = Cookies.get("role");
    if (role !== "employer") return;

    const handleLogoUpdate = (event) => {
      const empId = Cookies.get("employerId");
      if (!empId) return;

      const fetchEmployerInfo = async () => {
        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: empId,
              },
            }
          );
          if (response.ok) {
            const data = await response.json();
            setCandData({
              name: data.data?.emp_name || "Employer",
              profileImg: data.data?.company_logo
                ? (data.data.company_logo.startsWith("http")
                    ? data.data.company_logo
                    : `${import.meta.env.VITE_APP_HOST}/${data.data.company_logo}`)
                : data.data?.profile_picture
                ? (data.data.profile_picture.startsWith("http")
                    ? data.data.profile_picture
                    : `${import.meta.env.VITE_APP_HOST}/${data.data.profile_picture}`)
                : avatar,
            });
          }
        } catch (error) {
          console.error("Error fetching updated employer data:", error);
        }
      };

      fetchEmployerInfo();
    };

    window.addEventListener('companyLogoUpdated', handleLogoUpdate);

    return () => {
      window.removeEventListener('companyLogoUpdated', handleLogoUpdate);
    };
  }, []);

  const { onOpenSidenav, brandText } = props;
  const [darkmode, setDarkmode] = React.useState(false);

  return (
    <nav className="sticky top-0 z-40 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-b border-gray-200 dark:border-gray-800 shadow-sm">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left: Brand/Logo Section */}
          <div className="flex items-center gap-4">
            <button
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              onClick={onOpenSidenav}
            >
              <FiAlignJustify className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>
            
            <div className="hidden md:block">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                {brandText || 'Visume'}
              </h1>
            </div>
          </div>

          {/* Center: Navigation Items (if needed in future) */}
          <div className="hidden lg:flex items-center gap-6">
            {/* Add quick navigation items here if needed */}
          </div>

          {/* Right: Actions */}
          <div className="flex items-center gap-3">
            {/* Notifications */}
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative"
              title="Notifications"
            >
              <IoMdNotificationsOutline className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              {/* Notification badge */}
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full border-2 border-white dark:border-gray-900"></span>
            </button>

            {/* Dark mode toggle */}
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              onClick={() => {
                if (darkmode) {
                  document.body.classList.remove("dark");
                  setDarkmode(false);
                } else {
                  document.body.classList.add("dark");
                  setDarkmode(true);
                }
              }}
              title={darkmode ? "Switch to light mode" : "Switch to dark mode"}
            >
              {darkmode ? (
                <RiSunFill className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              ) : (
                <RiMoonFill className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              )}
            </button>

            {/* Profile Section */}
            {pathname === "/admin/dashboard" ? (
              <div className="flex items-center gap-3">
                <img
                  src="https://logodix.com/logo/1707094.png"
                  alt="admin-logo"
                  className="w-8 h-8 object-cover rounded-full border-2 border-gray-200 dark:border-gray-600"
                />
                <span className="hidden sm:block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Admin
                </span>
              </div>
            ) : !jstoken ? (
              <div className="flex items-center gap-2">
                <button
                  className="px-3 py-1.5 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                  onClick={() => navigate("/candidate/signUp")}
                >
                  Sign Up
                </button>
                <button
                  className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                  onClick={() => navigate("/candidate/signIn")}
                >
                  Sign In
                </button>
              </div>
            ) : (
              <Dropdown
                button={
                  <div className="flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                    <img
                      className="h-8 w-8 rounded-full border-2 border-gray-200 dark:border-gray-600 object-cover"
                      src={candData.profileImg}
                      alt={candData.name}
                    />
                    <div className="hidden sm:block text-left">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate max-w-24">
                        {candData.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {Cookies.get("role") === "jobseeker" ? "Job Seeker" : "Employer"}
                      </p>
                    </div>
                  </div>
                }
                children={
                  <div className="w-64 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
                      <div className="flex items-center gap-3">
                        <img
                          className="h-12 w-12 rounded-full object-cover border-2 border-white dark:border-gray-600 shadow-sm"
                          src={candData.profileImg}
                          alt={candData.name}
                        />
                        <div>
                          <p className="text-sm font-semibold text-gray-900 dark:text-white">
                            {candData.name}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {Cookies.get("role") === "jobseeker" ? "Job Seeker" : "Employer"}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-2">
                      <a
                        href={
                          Cookies.get("role") === "jobseeker"
                            ? "../candidate/settings"
                            : "../employer/settings"
                        }
                        className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                      >
                        <div className="w-4 h-4 rounded-full bg-gray-300 dark:bg-gray-600"></div>
                        Profile Settings
                      </a>
                      <button
                        onClick={deletecookies}
                        className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                      >
                        <div className="w-4 h-4 rounded-full bg-red-200 dark:bg-red-800"></div>
                        Sign Out
                      </button>
                    </div>
                  </div>
                }
                classNames={"py-2 top-12 -left-[200px] w-max"}
              />
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
