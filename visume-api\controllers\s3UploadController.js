const {
  S3Client,
  PutObjectCommand,
  HeadBucketCommand,
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  AbortMultipartUploadCommand,
  ListPartsCommand
} = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const dotenv = require("dotenv");

dotenv.config();

async function validateS3Connection() {
  // Validate environment variables
  const configStatus = {
    region: process.env.AWS_REGION ? '✓' : '✗',
    bucket: process.env.AWS_BUCKET_NAME ? '✓' : '✗',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID ? '✓' : '✗',
    secretKey: process.env.AWS_SECRET_ACCESS_KEY ? '✓' : '✗'
  };

  try {

    console.log('S3 Configuration Status:');
    console.log('- Region:', configStatus.region, process.env.AWS_REGION || 'missing');
    console.log('- Bucket:', configStatus.bucket, process.env.AWS_BUCKET_NAME || 'missing');
    console.log('- Access Key:', configStatus.accessKeyId);
    console.log('- Secret Key:', configStatus.secretKey);

    if (!process.env.AWS_REGION || !process.env.AWS_BUCKET_NAME) {
      throw new Error('Missing required AWS configuration');
    }

    // Test credentials
    await s3Client.config.credentials();
    console.log('AWS credentials validated');
    
    // Test bucket access
    const command = new HeadBucketCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
    });

    await s3Client.send(command);
    console.log('Successfully validated S3 bucket access');
    return {
      success: true,
      config: configStatus
    };
  } catch (err) {
    console.error('S3 validation error:', {
      name: err.name,
      message: err.message,
      code: err.code
    });
    return {
      success: false,
      error: err.name,
      message: err.message,
      code: err.code,
      config: configStatus
    };
  }
}

let s3Client = null;

// Initialize S3 client if environment variables are available
const initializeS3Client = () => {
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY || !process.env.AWS_REGION) {
    console.error('Missing required AWS configuration. Please ensure these environment variables are set:');
    console.error('- AWS_REGION');
    console.error('- AWS_ACCESS_KEY_ID');
    console.error('- AWS_SECRET_ACCESS_KEY');
    console.error('- AWS_BUCKET_NAME');
    return false;
  }

  try {
    s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });

    // Test the S3 client configuration
    s3Client.config.credentials().catch(err => {
      console.error('Failed to load AWS credentials:', err);
      s3Client = null;
      return false;
    });

    return true;
  } catch (err) {
    console.error('Error initializing S3 client:', err);
    s3Client = null;
    return false;
  }
};

// Initialize S3 on module load
const initialized = initializeS3Client();
if (!initialized) {
  console.error('Failed to initialize S3 client. S3 operations will not be available.');
}

// Health check endpoint
exports.checkS3Health = async function (req, res) {
  console.log('[DEBUG] S3 health check requested');
  
  if (!s3Client) {
    return res.status(503).json({
      status: 'unhealthy',
      error: 'S3ClientNotInitialized',
      message: 'S3 client is not initialized',
      configuration: null
    });
  }

  const validation = await validateS3Connection();
  
  if (validation.success) {
    res.status(200).json({
      status: 'healthy',
      message: 'S3 connection validated',
      configuration: validation.config
    });
  } else {
    res.status(503).json({
      status: 'unhealthy',
      error: validation.error,
      message: validation.message,
      code: validation.code,
      configuration: validation.config
    });
  }
};

exports.getS3Url = async function (req, res) {
  if (!s3Client) {
    return res.status(503).json({
      error: "Storage service unavailable",
      details: "S3 client is not initialized"
    });
  }

  console.log('Generating S3 URL for file:', req.params.fileName);
  try {
    const fileName = req.params.fileName;
    const contentType = req.query.contentType || "application/octet-stream";

    if (!process.env.AWS_BUCKET_NAME) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new PutObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: fileName,
      ContentType: contentType,
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn: 300 });
    res.status(200).json({ url });

  } catch (err) {
    console.error("Error generating signed URL:", err);

    if (err.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Invalid AWS credentials configuration"
      });
    }

    if (err.name === "NoSuchBucket") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Unable to access storage bucket. Please contact support."
      });
    }

    res.status(503).json({
      error: "Storage service unavailable",
      details: "Unable to access storage bucket. Please contact support."
    });
  }
};

// exports.getUploadUrlFromCacheOrNew = async function (req,res) {
//     if (!cachedUploadUrl || !cachedAuthToken) {
//       await generateNewUploadUrl();
//     } else {
//       try {
//         await axios.head(cachedUploadUrl);
//       } catch (error) {
//         if (
//           error.response &&
//           (error.response.status === 503 ||
//             error.response.code === "expired_auth_token")
//         ) {
//           await generateNewUploadUrl();
//         } else {
//           console.info("Using cached upload URL and auth token");
//         }
//       }
//     }
  
//       res.status(200).json({ uploadUrl: cachedUploadUrl, authToken: cachedAuthToken });
  
//   };
  
//   async function generateNewUploadUrl() {
//     const authDetails = await authorizeB2();
//     const uploadUrlData = await getUploadUrl(
//       authDetails.apiUrl,
//       authDetails.authToken
//     );
//     cachedUploadUrl = uploadUrlData.uploadUrl;
//     cachedAuthToken = uploadUrlData.authorizationToken;
//   }
  
//   async function authorizeB2() {
//     const authRes = await axios({
//       method: "GET",
//       url: `https://api.backblazeb2.com/b2api/v3/${apiMethodName}`,
//       auth: {
//         username: "005521a5a22d8eb0000000001", // Replace with your actual applicationKeyId
//         password: "K005nfgdB52wn6vv3Z+OCB1/E8jfBZg", // Replace with your actual applicationKey
//       },
//     });
  
//     console.info("Success getting B2 auth details");
  
//     const data = authRes.data;
//     console.log(data.apiInfo);
  
//     if (!data.apiInfo.storageApi.apiUrl) {
//       throw new Error(`Missing property "apiUrl" in ${apiMethodName} response.`);
//     }
//     if (!data.authorizationToken) {
//       throw new Error(
//         `Missing property "authorizationToken" in ${apiMethodName} response.`
//       );
//     }
  
//     return {
//       apiUrl: data.apiInfo.storageApi.apiUrl,
//       authToken: data.authorizationToken,
//     };
//   }
  
//   async function getUploadUrl(apiUrl, authToken) {
//     const uploadUrlRes = await axios({
//       method: "POST",
//       url: `${apiUrl}/b2api/v3/${getUploadUrlMethodName}`,
//       headers: {
//         Authorization: authToken,
//       },
//       data: {
//         bucketId: "f5b2512a154ad2129d080e1b", // Replace with your actual bucketId
//       },
//     });
  
//     console.info("Success getting upload URL");
  
//     const uploadUrlData = uploadUrlRes.data;
//     if (!uploadUrlData.uploadUrl) {
//       throw new Error(
//         `Missing property "uploadUrl" in ${getUploadUrlMethodName} response.`
//       );
//     }
  
//     return uploadUrlData;
//   }

// ============================================================================
// MULTIPART UPLOAD METHODS
// ============================================================================

// Initialize multipart upload
exports.initializeMultipartUpload = async function (req, res) {
  if (!s3Client) {
    return res.status(503).json({
      error: "Storage service unavailable",
      details: "S3 client is not initialized"
    });
  }

  console.log('Initializing multipart upload:', req.body);
  try {
    const { fileName, contentType, fileSize } = req.body;

    if (!fileName || !contentType) {
      return res.status(400).json({
        error: "Missing required parameters",
        details: "fileName and contentType are required"
      });
    }

    if (!process.env.AWS_BUCKET_NAME) {
      throw new Error("S3 bucket name not configured");
    }

    // Add file extension if not present
    let finalFileName = fileName;
    if (contentType === "video/webm" && !fileName.includes('.')) {
      finalFileName = `${fileName}.webm`;
    }

    const command = new CreateMultipartUploadCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: finalFileName,
      ContentType: contentType,
      Metadata: {
        originalSize: fileSize?.toString() || '0',
        uploadType: 'multipart'
      }
    });

    const response = await s3Client.send(command);

    console.log('Multipart upload initialized:', {
      uploadId: response.UploadId,
      key: response.Key
    });

    res.status(200).json({
      uploadId: response.UploadId,
      key: response.Key,
      bucket: process.env.AWS_BUCKET_NAME
    });

  } catch (err) {
    console.error("Error initializing multipart upload:", err);

    if (err.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Invalid AWS credentials configuration"
      });
    }

    if (err.name === "NoSuchBucket") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Unable to access storage bucket. Please contact support."
      });
    }

    res.status(500).json({
      error: "Failed to initialize multipart upload",
      details: err.message
    });
  }
};

// Generate pre-signed URL for uploading a specific part
exports.generatePartUploadUrl = async function (req, res) {
  if (!s3Client) {
    return res.status(503).json({
      error: "Storage service unavailable",
      details: "S3 client is not initialized"
    });
  }

  console.log('Generating part upload URL:', req.params, req.query);
  try {
    const { uploadId, partNumber } = req.params;
    const { key } = req.query;

    if (!uploadId || !partNumber || !key) {
      return res.status(400).json({
        error: "Missing required parameters",
        details: "uploadId, partNumber, and key are required"
      });
    }

    const partNum = parseInt(partNumber);
    if (isNaN(partNum) || partNum < 1 || partNum > 10000) {
      return res.status(400).json({
        error: "Invalid part number",
        details: "Part number must be between 1 and 10000"
      });
    }

    if (!process.env.AWS_BUCKET_NAME) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new UploadPartCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      UploadId: uploadId,
      PartNumber: partNum
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour expiration

    console.log(`Generated part upload URL for part ${partNum}`);

    res.status(200).json({
      url: url,
      partNumber: partNum
    });

  } catch (err) {
    console.error("Error generating part upload URL:", err);

    if (err.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Invalid AWS credentials configuration"
      });
    }

    res.status(500).json({
      error: "Failed to generate part upload URL",
      details: err.message
    });
  }
};

// Complete multipart upload by combining all parts
exports.completeMultipartUpload = async function (req, res) {
  if (!s3Client) {
    return res.status(503).json({
      error: "Storage service unavailable",
      details: "S3 client is not initialized"
    });
  }

  console.log('Completing multipart upload:', req.body);
  try {
    const { uploadId, key, parts } = req.body;

    if (!uploadId || !key || !parts || !Array.isArray(parts)) {
      return res.status(400).json({
        error: "Missing required parameters",
        details: "uploadId, key, and parts array are required"
      });
    }

    // Validate parts array
    for (const part of parts) {
      if (!part.PartNumber || !part.ETag) {
        return res.status(400).json({
          error: "Invalid parts data",
          details: "Each part must have PartNumber and ETag"
        });
      }
    }

    // Sort parts by PartNumber to ensure correct order
    const sortedParts = parts.sort((a, b) => a.PartNumber - b.PartNumber);

    if (!process.env.AWS_BUCKET_NAME) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new CompleteMultipartUploadCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: sortedParts
      }
    });

    const response = await s3Client.send(command);

    console.log('Multipart upload completed:', {
      location: response.Location,
      key: response.Key
    });

    // Return the final S3 URL (without query parameters)
    const finalUrl = `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;

    res.status(200).json({
      url: finalUrl,
      location: response.Location,
      key: response.Key,
      etag: response.ETag
    });

  } catch (err) {
    console.error("Error completing multipart upload:", err);

    if (err.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Invalid AWS credentials configuration"
      });
    }

    res.status(500).json({
      error: "Failed to complete multipart upload",
      details: err.message
    });
  }
};

// Abort multipart upload and cleanup
exports.abortMultipartUpload = async function (req, res) {
  if (!s3Client) {
    return res.status(503).json({
      error: "Storage service unavailable",
      details: "S3 client is not initialized"
    });
  }

  console.log('Aborting multipart upload:', req.params, req.body);
  try {
    const { uploadId } = req.params;
    const { key } = req.body;

    if (!uploadId || !key) {
      return res.status(400).json({
        error: "Missing required parameters",
        details: "uploadId and key are required"
      });
    }

    if (!process.env.AWS_BUCKET_NAME) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new AbortMultipartUploadCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      UploadId: uploadId
    });

    await s3Client.send(command);

    console.log('Multipart upload aborted:', { uploadId, key });

    res.status(200).json({
      message: "Multipart upload aborted successfully",
      uploadId: uploadId
    });

  } catch (err) {
    console.error("Error aborting multipart upload:", err);

    if (err.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Invalid AWS credentials configuration"
      });
    }

    res.status(500).json({
      error: "Failed to abort multipart upload",
      details: err.message
    });
  }
};

// List uploaded parts for resumption capability
exports.listUploadParts = async function (req, res) {
  if (!s3Client) {
    return res.status(503).json({
      error: "Storage service unavailable",
      details: "S3 client is not initialized"
    });
  }

  console.log('Listing upload parts:', req.params, req.query);
  try {
    const { uploadId } = req.params;
    const { key } = req.query;

    if (!uploadId || !key) {
      return res.status(400).json({
        error: "Missing required parameters",
        details: "uploadId and key are required"
      });
    }

    if (!process.env.AWS_BUCKET_NAME) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new ListPartsCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      UploadId: uploadId
    });

    const response = await s3Client.send(command);

    console.log(`Listed ${response.Parts?.length || 0} parts for upload ${uploadId}`);

    res.status(200).json({
      parts: response.Parts || [],
      uploadId: uploadId,
      key: key,
      isTruncated: response.IsTruncated || false,
      nextPartNumberMarker: response.NextPartNumberMarker
    });

  } catch (err) {
    console.error("Error listing upload parts:", err);

    if (err.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Invalid AWS credentials configuration"
      });
    }

    if (err.name === "NoSuchUpload") {
      return res.status(404).json({
        error: "Upload not found",
        details: "The specified multipart upload does not exist"
      });
    }

    res.status(500).json({
      error: "Failed to list upload parts",
      details: err.message
    });
  }
};
