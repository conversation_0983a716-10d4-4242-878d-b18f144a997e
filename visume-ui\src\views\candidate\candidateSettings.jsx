import { useEffect, useState } from "react";
import {
  EyeIcon,
  EyeOffIcon,
  User,
  Lock,
  Bell,
  Shield,
  Upload,
  FileText,
  Check,
  Camera,
  Settings,
} from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const CandidateSettings = () => {
  const navigate = useNavigate();
  const cand_id = Cookies.get("candId");

  // Active tab state
  const [activeTab, setActiveTab] = useState("profile");

  // Profile state
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    mobile: "",
    gender: "",
    language: "",
    location: "",
    profileImage: null,
  });

  // Password state
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Notification state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    jobAlerts: true,
    interviewReminders: true,
    marketingEmails: false,
  });

  // Privacy state
  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: "public",
    showContactInfo: true,
    allowDirectMessages: true,
  });

  // Loading states
  const [loading, setLoading] = useState({
    profile: false,
    password: false,
    notifications: false,
    privacy: false,
  });

  // Other states
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});
  const [isEditing, setIsEditing] = useState(false);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!cand_id) {
        toast.error("No Token Found, Please Login Again");
        navigate("/candidate/signIn");
        return;
      }

      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}`
        );
        const data = await response.json();

        if (data.candidateProfile?.[0]) {
          const candidate = data.candidateProfile[0];
          console.log("Candidate profile API response:", candidate);
          setProfileData({
            name: candidate.cand_name || "",
            email: candidate.cand_email || "",
            mobile: candidate.cand_mobile || "",
            language:
              candidate.languages_known?.replace(/^\["|"\]$/g, "") || "",
            location:
              candidate.preferred_location?.replace(/^\["|"\]$/g, "") || "",
            profileImage: candidate.profile_picture || null,
          });
        }
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchProfileData();
  }, [cand_id, navigate]);

  // Handle password change
  const handlePasswordChange = async () => {
    if (
      !passwordData.oldPassword ||
      !passwordData.newPassword ||
      !passwordData.confirmPassword
    ) {
      toast.error("Please fill in all password fields.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long.");
      return;
    }

    setLoading((prev) => ({ ...prev, password: true }));

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/changePassword`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cand_id,
            password: passwordData.oldPassword,
            newPassword: passwordData.newPassword,
          }),
        }
      );

      const responseData = await response.json();

      if (!response.ok) {
        toast.error(responseData.message);
      } else {
        setPasswordData({
          oldPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        toast.success("Password updated successfully!");
      }
    } catch (error) {
      toast.error("An unexpected error occurred.");
      console.error("Error:", error);
    } finally {
      setLoading((prev) => ({ ...prev, password: false }));
    }
  };

  // Handle profile update
  const handleProfileUpdate = async () => {
    setLoading((prev) => ({ ...prev, profile: true }));

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cand_name: profileData.name,
            cand_email: profileData.email,
            cand_mobile: profileData.mobile,
            languages_known: profileData.language,
            preferred_location: profileData.location,
            profile_picture: profileData.profileImage,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok) {
        toast.error(data.message || "Failed to update profile");
      } else {
        toast.success("Profile updated successfully!");
        setIsEditing(false);
      }
    } catch (error) {
      toast.error("Failed to update profile");
    } finally {
      setLoading((prev) => ({ ...prev, profile: false }));
    }
  };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf && file.size <= 5 * 1024 * 1024) {
        // 5MB limit
        setUploadedFile(file);
        setErrors((prev) => ({ ...prev, resume: "" }));

        // Upload resume to server
        const formData = new FormData();
        formData.append("resume", file);
        formData.append("cand_id", cand_id);

        setLoading((prev) => ({ ...prev, profile: true }));
        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/uploadResume`,
            {
              method: "POST",
              body: formData,
            }
          );
          const data = await response.json();
          if (!response.ok) {
            setErrors((prev) => ({
              ...prev,
              resume: data.message || "Resume upload failed.",
            }));
            setUploadedFile(null);
          } else {
            toast.success("Resume uploaded successfully!");
          }
        } catch (error) {
          setErrors((prev) => ({
            ...prev,
            resume: "Resume upload failed.",
          }));
          setUploadedFile(null);
        } finally {
          setLoading((prev) => ({ ...prev, profile: false }));
        }
      } else {
        setErrors((prev) => ({
          ...prev,
          resume: "Please upload a valid PDF file under 5MB.",
        }));
        setUploadedFile(null);
      }
    }
  };

  // Handle profile image upload
  const handleProfileImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith("image/") && file.size <= 2 * 1024 * 1024) {
        // 2MB limit
        const formData = new FormData();
        formData.append("profile_picture", file);
        formData.append("cand_id", cand_id);

        setLoading((prev) => ({ ...prev, profile: true }));
        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/uploadProfileImage`,
            {
              method: "POST",
              body: formData,
            }
          );
          const data = await response.json();
          if (!response.ok) {
            toast.error(data.message || "Profile image upload failed.");
          } else {
            setProfileData((prev) => ({
              ...prev,
              profileImage: data.profile_picture_url || prev.profileImage,
            }));
            toast.success("Profile image updated!");
          }
        } catch (error) {
          toast.error("Profile image upload failed.");
        } finally {
          setLoading((prev) => ({ ...prev, profile: false }));
        }
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Tab configuration
  const tabs = [
    { id: "profile", label: "Profile", icon: User },
    { id: "security", label: "Security", icon: Lock },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy", icon: Shield },
  ];

  return (
    <div className="p-4 space-y-8">
      {/* Enhanced Header with Dashboard Styling */}
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
        <div className="flex items-center justify-between p-6">
          {/* Left: Profile Section */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 p-2.5 text-white">
                <Settings className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Account Settings
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Manage your account preferences and security settings
                </p>
              </div>
            </div>
          </div>

          {/* Right: Quick Stats */}
          <div className="hidden lg:flex items-center gap-6">
            <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-3">
              <div className="p-1.5 bg-white dark:bg-gray-700 rounded-lg">
                <User className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </div>
              <div>
                <div className="text-xl font-bold text-gray-900 dark:text-white leading-none">
                  {profileData.name || 'Profile'}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {profileData.email || 'Loading...'}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Profile Info */}
        <div className="lg:hidden border-t border-gray-200 dark:border-gray-800 p-4">
          <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
            <div className="p-1 bg-white dark:bg-gray-700 rounded">
              <User className="w-3 h-3 text-gray-600 dark:text-gray-400" />
            </div>
            <div>
              <div className="text-lg font-bold text-gray-900 dark:text-white leading-none">
                {profileData.name || 'Profile'}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {profileData.email || 'Loading...'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-800">
          {/* Enhanced Tab Navigation */}
          <nav className="flex flex-wrap gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
                      : 'text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <div className={`p-1 rounded ${isActive ? 'bg-white/20' : 'bg-gray-100 dark:bg-gray-800'}`}>
                    <Icon className={`h-3 w-3 ${isActive ? 'text-white' : 'text-gray-600 dark:text-gray-400'}`} />
                  </div>
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
        <div className="p-6">

          {/* Content Area */}
          <div>
            {/* Profile Tab */}
            {activeTab === "profile" && (
              <div>
                {/* Enhanced Section Header */}
                <div className="mb-8 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-lg">
                      <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Profile Information
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Update your personal information and profile settings
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() =>
                      isEditing ? handleProfileUpdate() : setIsEditing(true)
                    }
                    disabled={loading.profile}
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {loading.profile ? (
                      <>
                        <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                        Saving...
                      </>
                    ) : isEditing ? (
                      <>
                        <Check className="h-4 w-4" />
                        Save Changes
                      </>
                    ) : (
                      "Edit Profile"
                    )}
                  </button>
                </div>

                {/* Enhanced Profile Image Section */}
                <div className="mb-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-100 dark:border-blue-800/50">
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      <img
                        src={
                          profileData.profileImage
                            ? profileData.profileImage.startsWith("http")
                              ? profileData.profileImage
                              : `${import.meta.env.VITE_APP_HOST}/${profileData.profileImage.replace(/^\/+/, "")}`
                            : "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        }
                        alt="Profile"
                        className="h-24 w-24 rounded-full border-4 border-white object-cover shadow-lg dark:border-gray-700"
                      />
                      {isEditing && (
                        <label className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-full bg-black bg-opacity-50 opacity-0 transition-opacity duration-200 hover:opacity-100">
                          <Camera className="h-6 w-6 text-white" />
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleProfileImageUpload}
                            className="hidden"
                          />
                        </label>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {profileData.name || 'Your Name'}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {profileData.email || '<EMAIL>'}
                      </p>
                      {isEditing && (
                        <div className="mt-2 flex items-center gap-2">
                          <Camera className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                            Click on image to change profile photo
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Enhanced Profile Form */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={profileData.name}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 text-gray-900 dark:text-white transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-gray-50 disabled:text-gray-500 dark:disabled:bg-gray-900 disabled:cursor-not-allowed"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                      Email Address
                    </label>
                    <div className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 px-4 py-3 text-gray-900 dark:text-white flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                      <span>{profileData.email}</span>
                      <span className="ml-auto text-xs text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-800 px-2 py-1 rounded">Read-only</span>
                    </div>
                  </div>

                  <div>
                    <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                      Preferred Location
                    </label>
                    <input
                      type="text"
                      value={profileData.location}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          location: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      placeholder="e.g., New York, Remote, Bangalore"
                      className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 text-gray-900 dark:text-white transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-gray-50 disabled:text-gray-500 dark:disabled:bg-gray-900 disabled:cursor-not-allowed"
                    />
                  </div>

                  <div>
                    <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                      Mobile Number
                    </label>
                    <input
                      type="tel"
                      value={profileData.mobile}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          mobile: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 text-gray-900 dark:text-white transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-gray-50 disabled:text-gray-500 dark:disabled:bg-gray-900 disabled:cursor-not-allowed"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                      Languages Known
                    </label>
                    <input
                      type="text"
                      value={profileData.language}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          language: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      placeholder="e.g., English, Hindi, Spanish"
                      className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 text-gray-900 dark:text-white transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 disabled:bg-gray-50 disabled:text-gray-500 dark:disabled:bg-gray-900 disabled:cursor-not-allowed"
                    />
                  </div>
                </div>

                {/* Enhanced Resume Section */}
                <div className="mt-8 bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 rounded-lg">
                      <FileText className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Resume Management
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Manage your resume document
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    <a
                      href="/resume"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 rounded-lg bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 px-4 py-2 font-medium text-gray-700 dark:text-gray-300 transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                    >
                      <FileText className="h-4 w-4" />
                      View Current Resume
                    </a>

                    {isEditing && (
                      <div className="flex-1">
                        <label className="inline-flex cursor-pointer items-center gap-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                          <Upload className="h-4 w-4" />
                          Upload New Resume
                          <input
                            type="file"
                            accept=".pdf,.doc,.docx"
                            onChange={handleFileUpload}
                            className="hidden"
                          />
                        </label>
                        {uploadedFile && (
                          <div className="mt-2 flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                            <Check className="h-4 w-4" />
                            <span>{uploadedFile.name} uploaded successfully</span>
                          </div>
                        )}
                        {errors.resume && (
                          <p className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center gap-2">
                            <span className="w-4 h-4 rounded-full bg-red-100 dark:bg-red-900/50 flex items-center justify-center text-xs">!</span>
                            {errors.resume}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Security Tab */}
            {activeTab === "security" && (
              <div>
                {/* Enhanced Section Header */}
                <div className="mb-8 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-red-100 to-orange-100 dark:from-red-900/50 dark:to-orange-900/50 rounded-lg">
                    <Lock className="w-5 h-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Security Settings
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Update your password and security preferences
                    </p>
                  </div>
                </div>

                <div className="max-w-2xl">
                  {/* Password Change Card */}
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-sm">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-lg">
                        <Lock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Change Password
                      </h3>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                          Current Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.old ? "text" : "password"}
                            value={passwordData.oldPassword}
                            onChange={(e) =>
                              setPasswordData((prev) => ({
                                ...prev,
                                oldPassword: e.target.value,
                              }))
                            }
                            className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 pr-12 text-gray-900 dark:text-white transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                            placeholder="Enter your current password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowPasswords((prev) => ({
                                ...prev,
                                old: !prev.old,
                              }))
                            }
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                          >
                            {showPasswords.old ? (
                              <EyeOffIcon className="h-5 w-5" />
                            ) : (
                              <EyeIcon className="h-5 w-5" />
                            )}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                          New Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.new ? "text" : "password"}
                            value={passwordData.newPassword}
                            onChange={(e) =>
                              setPasswordData((prev) => ({
                                ...prev,
                                newPassword: e.target.value,
                              }))
                            }
                            className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 pr-12 text-gray-900 dark:text-white transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                            placeholder="Enter a new password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowPasswords((prev) => ({
                                ...prev,
                                new: !prev.new,
                              }))
                            }
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                          >
                            {showPasswords.new ? (
                              <EyeOffIcon className="h-5 w-5" />
                            ) : (
                              <EyeIcon className="h-5 w-5" />
                            )}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="mb-2 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                          Confirm New Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.confirm ? "text" : "password"}
                            value={passwordData.confirmPassword}
                            onChange={(e) =>
                              setPasswordData((prev) => ({
                                ...prev,
                                confirmPassword: e.target.value,
                              }))
                            }
                            className="w-full rounded-lg border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 pr-12 text-gray-900 dark:text-white transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                            placeholder="Confirm your new password"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              setShowPasswords((prev) => ({
                                ...prev,
                                confirm: !prev.confirm,
                              }))
                            }
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                          >
                            {showPasswords.confirm ? (
                              <EyeOffIcon className="h-5 w-5" />
                            ) : (
                              <EyeIcon className="h-5 w-5" />
                            )}
                          </button>
                        </div>
                      </div>

                      <button
                        onClick={handlePasswordChange}
                        disabled={loading.password}
                        className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                      >
                        {loading.password ? (
                          <>
                            <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                            Updating...
                          </>
                        ) : (
                          "Update Password"
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Enhanced Security Tips */}
                  <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800/50">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-lg">
                        <Shield className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-300">
                        Password Security Tips
                      </h4>
                    </div>
                    <ul className="space-y-3 text-sm text-blue-800 dark:text-blue-400">
                      <li className="flex items-start gap-3">
                        <div className="mt-0.5 p-1 bg-green-100 dark:bg-green-900/50 rounded-full">
                          <Check className="h-3 w-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span>Use at least 8 characters with a mix of letters, numbers, and symbols</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="mt-0.5 p-1 bg-green-100 dark:bg-green-900/50 rounded-full">
                          <Check className="h-3 w-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span>Avoid using personal information like names or birthdays</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="mt-0.5 p-1 bg-green-100 dark:bg-green-900/50 rounded-full">
                          <Check className="h-3 w-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span>Don't reuse passwords from other accounts</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="mt-0.5 p-1 bg-green-100 dark:bg-green-900/50 rounded-full">
                          <Check className="h-3 w-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span>Consider using a password manager for better security</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Notifications Tab */}
            {activeTab === "notifications" && (
              <div>
                {/* Enhanced Section Header */}
                <div className="mb-8 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-yellow-100 to-orange-100 dark:from-yellow-900/50 dark:to-orange-900/50 rounded-lg">
                    <Bell className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Notification Preferences
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Manage how you receive notifications and updates
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  {[
                    {
                      key: "emailNotifications",
                      label: "Email Notifications",
                      description: "Receive notifications via email",
                      icon: "📧",
                    },
                    {
                      key: "jobAlerts",
                      label: "Job Alerts",
                      description: "Get notified about new job opportunities",
                      icon: "💼",
                    },
                    {
                      key: "interviewReminders",
                      label: "Interview Reminders",
                      description: "Receive reminders about upcoming interviews",
                      icon: "⏰",
                    },
                    {
                      key: "marketingEmails",
                      label: "Marketing Emails",
                      description: "Receive promotional emails and newsletters",
                      icon: "📢",
                    },
                  ].map((setting) => (
                    <div
                      key={setting.key}
                      className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{setting.icon}</div>
                          <div>
                            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                              {setting.label}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {setting.description}
                            </p>
                          </div>
                        </div>
                        <label className="relative inline-flex cursor-pointer items-center">
                          <input
                            type="checkbox"
                            checked={notificationSettings[setting.key]}
                            onChange={(e) =>
                              setNotificationSettings((prev) => ({
                                ...prev,
                                [setting.key]: e.target.checked,
                              }))
                            }
                            className="peer sr-only"
                          />
                          <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-gradient-to-r peer-checked:from-blue-600 peer-checked:to-purple-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-blue-800"></div>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-8 flex justify-end">
                  <button
                    onClick={async () => {
                      setLoading((prev) => ({ ...prev, notifications: true }));
                      try {
                        const response = await fetch(
                          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}/notifications`,
                          {
                            method: "PUT",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify(notificationSettings),
                          }
                        );
                        const data = await response.json();
                        if (!response.ok) {
                          toast.error(data.message || "Failed to update notifications");
                        } else {
                          toast.success("Notification preferences updated!");
                        }
                      } catch (error) {
                        toast.error("Failed to update notifications");
                      } finally {
                        setLoading((prev) => ({ ...prev, notifications: false }));
                      }
                    }}
                    disabled={loading.notifications}
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {loading.notifications ? (
                      <>
                        <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      "Save Preferences"
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Enhanced Privacy Tab */}
            {activeTab === "privacy" && (
              <div>
                {/* Enhanced Section Header */}
                <div className="mb-8 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 rounded-lg">
                    <Shield className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Privacy Settings
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Control who can see your information and how it's used
                    </p>
                  </div>
                </div>

                <div className="space-y-8">
                  {/* Profile Visibility Card */}
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-sm">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-lg">
                        <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Profile Visibility
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {[
                        {
                          value: "public",
                          label: "Public",
                          description: "Anyone can view your profile",
                          icon: "🌍",
                        },
                        {
                          value: "employers",
                          label: "Employers Only",
                          description: "Only verified employers can view your profile",
                          icon: "🏢",
                        },
                        {
                          value: "private",
                          label: "Private",
                          description: "Only you can view your profile",
                          icon: "🔒",
                        },
                      ].map((option) => (
                        <label
                          key={option.value}
                          className={`flex cursor-pointer items-center rounded-lg border-2 p-4 transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 ${
                            privacySettings.profileVisibility === option.value
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400'
                              : 'border-gray-200 dark:border-gray-700'
                          }`}
                        >
                          <input
                            type="radio"
                            name="profileVisibility"
                            value={option.value}
                            checked={privacySettings.profileVisibility === option.value}
                            onChange={(e) =>
                              setPrivacySettings((prev) => ({
                                ...prev,
                                profileVisibility: e.target.value,
                              }))
                            }
                            className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600"
                          />
                          <div className="ml-3 flex items-center gap-3">
                            <div className="text-2xl">{option.icon}</div>
                            <div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-white">
                                {option.label}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {option.description}
                              </div>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Contact Settings Card */}
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-sm">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="p-2 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-lg">
                        <Bell className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Contact Preferences
                      </h3>
                    </div>
                    <div className="space-y-4">
                      {[
                        {
                          key: "showContactInfo",
                          label: "Show Contact Information",
                          description: "Allow others to see your email and phone number",
                          icon: "📞",
                        },
                        {
                          key: "allowDirectMessages",
                          label: "Allow Direct Messages",
                          description: "Let employers send you messages directly",
                          icon: "💬",
                        },
                      ].map((setting) => (
                        <div
                          key={setting.key}
                          className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="text-2xl">{setting.icon}</div>
                              <div>
                                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                                  {setting.label}
                                </h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {setting.description}
                                </p>
                              </div>
                            </div>
                            <label className="relative inline-flex cursor-pointer items-center">
                              <input
                                type="checkbox"
                                checked={privacySettings[setting.key]}
                                onChange={(e) =>
                                  setPrivacySettings((prev) => ({
                                    ...prev,
                                    [setting.key]: e.target.checked,
                                  }))
                                }
                                className="peer sr-only"
                              />
                              <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-gradient-to-r peer-checked:from-blue-600 peer-checked:to-purple-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-blue-800"></div>
                            </label>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={async () => {
                        setLoading((prev) => ({ ...prev, privacy: true }));
                        try {
                          const response = await fetch(
                            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}/privacy`,
                            {
                              method: "PUT",
                              headers: { "Content-Type": "application/json" },
                              body: JSON.stringify(privacySettings),
                            }
                          );
                          const data = await response.json();
                          if (!response.ok) {
                            toast.error(data.message || "Failed to update privacy settings");
                          } else {
                            toast.success("Privacy settings updated!");
                          }
                        } catch (error) {
                          toast.error("Failed to update privacy settings");
                        } finally {
                          setLoading((prev) => ({ ...prev, privacy: false }));
                        }
                      }}
                      disabled={loading.privacy}
                      className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {loading.privacy ? (
                        <>
                          <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                          Saving...
                        </>
                      ) : (
                        "Save Settings"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateSettings;
