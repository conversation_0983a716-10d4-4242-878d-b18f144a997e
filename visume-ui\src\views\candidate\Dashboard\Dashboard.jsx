// Dashboard.jsx
import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import DashboardContent from "./DashboardContent";
import avatar from "assets/img/avatars/avatar4.png";
import { Eye, ClipboardCheck, MousePointerClick, Unlock } from "lucide-react";
import { getFormattedMembershipStatus } from "../../../services/membershipService";

const Dashboard = () => {
  const jstoken = Cookies.get("jstoken");
  const candId = Cookies.get("candId");
  const [profile_picture, setProfilePicture] = useState(avatar);
  const [imageError, setImageError] = useState(false);

  const [videoProfiles, setVideoProfiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [userStats, setUserStats] = useState([
    {
      count: 15,
      label: "Shortlists",
      Icon: ClipboardCheck,
    },
    {
      count: 23,
      label: "Views",
      Icon: Eye,
    },
    {
      count: 23,
      label: "Clicks",
      Icon: MousePointerClick,
    },
    {
      count: 3,
      label: "UnLocked",
      Icon: Unlock,
    },
  ]);
  const [candData, setCandData] = useState("");
  const [loadingInfo, setLoadingInfo] = useState(false);
  const [jobData, setJobData] = useState([]);
  const [membershipStatus, setMembershipStatus] = useState(null);
  const [membershipLoading, setMembershipLoading] = useState(false);

  useEffect(() => {
    const fetchJobData = async () => {
      try {
        const resjobdata = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/suggestedJobs`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (resjobdata.ok) {
          const data = await resjobdata.json();
          setJobData(Array.isArray(data) ? data : []);
        }
      } catch (error) {
        // handle error
      }
    };
    fetchJobData();
  }, []);

  // 🎯 MEMBERSHIP STATUS: Fetch candidate's membership status and limits
  useEffect(() => {
    const fetchMembershipStatus = async () => {
      if (!candId) return;

      try {
        setMembershipLoading(true);
        const status = await getFormattedMembershipStatus(candId);
        setMembershipStatus(status);
      } catch (error) {
        console.error('Error fetching membership status:', error);
        // Set default status on error
        setMembershipStatus({
          canCreateVisume: true,
          currentVisumeCount: 0,
          allowedVisumes: 1,
          planName: 'Free Plan',
          error: true
        });
      } finally {
        setMembershipLoading(false);
      }
    };

    fetchMembershipStatus();
  }, [candId]);

  useEffect(() => {
    const fetchCandidateInfo = async () => {
      try {
        setLoadingInfo(true);
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (response.ok) {
          const data = await response.json();
          const newProfiles = data.videoProfiles.map((profile) => ({
            vpid: profile.video_profile_id,
            role: profile.role,
            skills: profile.skills.split(",").map((skill) => skill.trim()),
            status: profile.status,
          }));
          setVideoProfiles((prevProfiles) => [...prevProfiles, ...newProfiles]);
          setCandData(data.candidateProfile[0]);
          setUserStats([
            {
              count: data.candidateProfile[0].statusCounts.shortlisted,
              label: "Shortlists",
              Icon: ClipboardCheck,
            },
            {
              count: data.candidateProfile[0].interactions.view,
              label: "Views",
              Icon: Eye,
            },
            {
              count: data.candidateProfile[0].interactions.click,
              label: "Clicks",
              Icon: MousePointerClick,
            },
            {
              count: data.candidateProfile[0].statusCounts.unlocked,
              label: "UnLocked",
              Icon: Unlock,
            },
          ]);
          if (data.candidateProfile[0].profile_picture) {
            const pic = data.candidateProfile[0].profile_picture;
            if (pic.startsWith("http")) {
              setProfilePicture(pic);
            } else {
              setProfilePicture(`${import.meta.env.VITE_APP_HOST}/${pic}`);
            }
          }
        }
      } catch (error) {
        // handle error
      }
      setLoadingInfo(false);
    };
    if (candId) {
      fetchCandidateInfo();
    }
  }, [candId]);

  const [createVRpopup, setcreateVRpopup] = useState(false);
  const [showVideoProfilePopup, setShowVideoProfilePopup] = useState(false);

  const togglePopupVR = () => {
    setcreateVRpopup(!createVRpopup);
  };

  const toggleVideoProfilePopup = () => {
    setShowVideoProfilePopup(!showVideoProfilePopup);
  };

  return (
    <DashboardContent
      jstoken={jstoken}
      candData={candData}
      loadingInfo={loadingInfo}
      togglePopupVR={togglePopupVR}
      profile_picture={profile_picture}
      imageError={imageError}
      setImageError={setImageError}
      userStats={userStats}
      jobData={jobData}
      videoProfiles={videoProfiles}
      isLoading={isLoading}
      createVRpopup={createVRpopup}
      showVideoProfilePopup={showVideoProfilePopup}
      toggleVideoProfilePopup={toggleVideoProfilePopup}
      membershipStatus={membershipStatus}
      membershipLoading={membershipLoading}
    />
  );
};

export default Dashboard;
