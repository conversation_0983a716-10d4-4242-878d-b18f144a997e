// AiAssistance.jsx
import React, { useState, useRef, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Loader2, Send, Sparkle } from "lucide-react";

// Modern, visually rich chat UI with preserved API and markdown logic
const AiAssistance = ({
  messages,
  setMessages,
  input,
  setInput,
  context = {},
}) => {
  const [loading, setLoading] = useState(false);
  const chatRef = useRef(null);

  const GEMINI_API_URL = `${
    import.meta.env.VITE_APP_HOST
  }/api/gemini-assist/assist`;

  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTo({
        top: chatRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [messages, loading]);

  const handleSend = async () => {
    if (!input.trim() || loading) return;
    const newMessages = [...messages, { role: "user", text: input }];
    setMessages(newMessages);
    setInput("");
    setLoading(true);
    try {
      const res = await fetch(GEMINI_API_URL, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: newMessages,
          context,
        }),
      });
      const data = await res.json();
      const aiText =
        data?.candidates?.[0]?.content?.parts?.[0]?.text ||
        "No response from Gemini API.";
      setMessages([...newMessages, { role: "model", text: aiText }]);
    } catch (err) {
      setMessages([
        ...newMessages,
        { role: "model", text: "Error connecting to Gemini API." },
      ]);
    }
    setLoading(false);
  };

  const handleClear = () => {
    setMessages([]);
    setInput("");
  };

  // Quick actions for context-aware prompts
  const quickActions = [
    {
      label: "Candidate Overview",
      action: async () => {
        if (loading) return;
        // Show loading bubble immediately
        setMessages([{ role: "model", text: "AI is thinking..." }]);
        setLoading(true);
        let sysText = `Full Candidate Data:\n`;
        if (context?.candidateProf) {
          sysText += `Name: ${context.candidateProf.name || ""}\n`;
          sysText += `Email: ${context.candidateProf.email || ""}\n`;
          sysText += `Phone: ${context.candidateProf.phone || ""}\n`;
          sysText += `Location: ${context.candidateProf.location || ""}\n`;
        }
        if (context?.profileData) {
          sysText += `Profile Data: ${JSON.stringify(context.profileData)}\n`;
        }
        if (context?.strippedResumeJson) {
          sysText += `Summary: ${
            context.strippedResumeJson.summary ?? "Not available"
          }\n`;
          if (
            Array.isArray(context.strippedResumeJson.skills) &&
            context.strippedResumeJson.skills.length > 0
          ) {
            sysText += `Skills: ${context.strippedResumeJson.skills.join(
              ", "
            )}\n`;
          }
          if (
            Array.isArray(context.strippedResumeJson.experience) &&
            context.strippedResumeJson.experience.length > 0
          ) {
            sysText += `Experience:\n`;
            context.strippedResumeJson.experience.forEach((exp) => {
              sysText += `- ${exp.title || exp.position || "Role"} at ${
                exp.company || "Company"
              } (${exp.duration || "Duration"})\n`;
              if (exp.description) {
                sysText += `  - ${exp.description}\n`;
              }
            });
          }
          if (
            Array.isArray(context.strippedResumeJson.education) &&
            context.strippedResumeJson.education.length > 0
          ) {
            sysText += `Education:\n`;
            context.strippedResumeJson.education.forEach((edu) => {
              sysText += `- ${edu.degree || "Degree"} at ${
                edu.institution || "Institution"
              } (${edu.year || "Year"})\n`;
            });
          }
        }
        if (context?.questionsAndAnswers?.length > 0) {
          sysText += `Video Profile Q&A:\n`;
          context.questionsAndAnswers.forEach((qa, i) => {
            sysText += `Q${i + 1}: ${qa.question}\nA: ${qa.answer}\n`;
          });
        }
        try {
          const res = await fetch(GEMINI_API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              messages: [
                {
                  role: "user",
                  text:
                    sysText.trim() +
                    "\n\nSummarize this candidate's profile, background, skills, and video interview based on all provided data.",
                },
              ],
              context,
            }),
          });
          const data = await res.json();
          const aiText =
            data?.candidates?.[0]?.content?.parts?.[0]?.text ||
            "No response from Gemini API.";
          setMessages([{ role: "model", text: aiText }]);
        } catch (err) {
          setMessages([
            {
              role: "model",
              text: "Error connecting to Gemini API.",
            },
          ]);
        }
        setLoading(false);
      },
    },
    {
      label: "Technical Skills",
      action: async () => {
        if (loading) return;
        setMessages([{ role: "model", text: "AI is thinking..." }]);
        setLoading(true);
        let sysText = `Candidate Technical Skills:\n`;
        if (context?.strippedResumeJson) {
          if (
            Array.isArray(context.strippedResumeJson.skills) &&
            context.strippedResumeJson.skills.length > 0
          ) {
            sysText += `Skills: ${context.strippedResumeJson.skills.join(
              ", "
            )}\n`;
          }
          if (
            Array.isArray(context.strippedResumeJson.experience) &&
            context.strippedResumeJson.experience.length > 0
          ) {
            sysText += `Experience:\n`;
            context.strippedResumeJson.experience.forEach((exp) => {
              sysText += `- ${exp.title || exp.position || "Role"} at ${
                exp.company || "Company"
              } (${exp.duration || "Duration"})\n`;
              if (exp.description) {
                sysText += `  - ${exp.description}\n`;
              }
            });
          }
        }
        try {
          const res = await fetch(GEMINI_API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              messages: [
                {
                  role: "user",
                  text:
                    sysText.trim() +
                    "\n\nSummarize this candidate's technical skills and relevant experience.",
                },
              ],
              context,
            }),
          });
          const data = await res.json();
          const aiText =
            data?.candidates?.[0]?.content?.parts?.[0]?.text ||
            "No response from Gemini API.";
          setMessages([{ role: "model", text: aiText }]);
        } catch (err) {
          setMessages([
            {
              role: "model",
              text: "Error connecting to Gemini API.",
            },
          ]);
        }
        setLoading(false);
      },
    },
    {
      label: "Interview Summary",
      action: async () => {
        if (loading) return;
        setMessages([{ role: "model", text: "AI is thinking..." }]);
        setLoading(true);
        let sysText = `Candidate Video Resume Q&A:\n`;
        if (context?.questionsAndAnswers?.length > 0) {
          context.questionsAndAnswers.forEach((qa, i) => {
            sysText += `Q${i + 1}: ${qa.question}\nA: ${qa.answer}\n`;
          });
        } else {
          sysText += "No video Q&A available.\n";
        }
        try {
          const res = await fetch(GEMINI_API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              messages: [
                {
                  role: "user",
                  text:
                    sysText.trim() +
                    "\n\nSummarize this candidate's video resume and interview responses.",
                },
              ],
              context,
            }),
          });
          const data = await res.json();
          const aiText =
            data?.candidates?.[0]?.content?.parts?.[0]?.text ||
            "No response from Gemini API.";
          setMessages([{ role: "model", text: aiText }]);
        } catch (err) {
          setMessages([
            {
              role: "model",
              text: "Error connecting to Gemini API.",
            },
          ]);
        }
        setLoading(false);
      },
    },
  ];

  return (
    <div className="overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-slate-200">
      {/* Header */}
      <div className="border-b border-slate-200 bg-slate-50 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-slate-200">
              <Sparkle className="h-5 w-5 text-slate-700" />
            </div>
            <h2 className="text-lg font-semibold text-slate-900">AI Assistant</h2>
          </div>
          <button
            className="inline-flex items-center rounded-lg border border-slate-300 bg-white px-4 py-2 text-sm font-medium text-slate-700 shadow-sm transition-all hover:bg-slate-100 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            onClick={handleClear}
            disabled={loading || messages.length === 0}
          >
            Clear Chat
          </button>
        </div>
      </div>

      {/* Chat Area */}
      <div
        ref={chatRef}
        className="h-96 space-y-4 overflow-y-auto bg-gray-50 p-4"
      >
        {messages.length === 0 ? (
          <div className="flex h-full flex-col items-center justify-center text-center">
            <div className="mb-6">
              <div className="mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
                <Sparkle className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900">
                Ask me anything
              </h3>
              <p className="text-sm text-gray-600">
                Click below to help you understand candidate background, skills,
                and interview responses.
              </p>
            </div>
            <div className="w-full max-w-sm space-y-2">
              {quickActions.map((action, idx) => (
                <button
                  key={idx}
                  className="w-full rounded-lg border border-slate-200 bg-white p-3 text-left text-sm transition-all hover:border-slate-300 hover:bg-slate-100"
                  onClick={action.action}
                  disabled={loading}
                >
                  {action.label}
                </button>
              ))}
            </div>
          </div>
        ) : (
          <>
            {messages.map((msg, idx) => (
              <div
                key={idx}
                className={`flex ${
                  msg.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`max-w-[85%] rounded-xl px-4 py-3 ${
                    msg.role === "user"
                      ? "ml-auto bg-blue-600 text-white"
                      : "border border-slate-200 bg-white text-slate-900"
                  }`}
                >
                  <div className="whitespace-pre-wrap text-sm leading-relaxed">
                    {msg.role === "model" ? (
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {msg.text}
                      </ReactMarkdown>
                    ) : (
                      msg.text
                    )}
                  </div>
                </div>
              </div>
            ))}
            {loading && (
              <div className="flex justify-start">
                <div className="flex items-center space-x-2 rounded-xl border border-slate-200 bg-white px-4 py-3">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  <span className="text-sm text-slate-600">
                    AI is thinking...
                  </span>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-200 bg-white p-4">
        <div className="flex space-x-3">
          <input
            type="text"
            className="focus:border-transparent flex-1 rounded-lg border border-slate-300 px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Ask about anything...."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSend();
              }
            }}
            disabled={loading}
          />
          <button
            className={`flex items-center space-x-2 rounded-lg px-6 py-3 font-semibold transition-all ${
              loading || !input.trim()
                ? "cursor-not-allowed bg-slate-100 text-slate-400"
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
            onClick={handleSend}
            disabled={loading || !input.trim()}
          >
            {loading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
            <span>Send</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AiAssistance;
