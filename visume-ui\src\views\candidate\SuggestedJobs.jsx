import React, { useState, useEffect } from "react";
import Cookies from "js-cookie";
import JobCard from "./smaller_comp/SuggestedJobCard";
import toast from "react-hot-toast";
import SmLoader from "../../components/SmLoader";
import { 
  HiOutlineSparkles, 
  HiBriefcase 
} from "react-icons/hi";
import { 
  Search, 
  Filter, 
  MapPin, 
  Clock, 
  DollarSign,
  Briefcase,
  SlidersHorizontal,
  Grid3X3,
  List,
  ChevronDown
} from "lucide-react";

function SuggestedJobs() {
  const [jobs, setJobs] = useState([]);
  const [filteredJobs, setFilteredJobs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("");
  const [selectedType, setSelectedType] = useState("");
  const [selectedExperience, setSelectedExperience] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState("grid");
  const [sortBy, setSortBy] = useState("relevance");
  
  const jstoken = Cookies.get("jstoken");

  useEffect(() => {
    const fetchJobData = async () => {
      setIsLoading(true);
      try {
        const resjobdata = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/suggestedJobs`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (resjobdata.ok) {
          const data = await resjobdata.json();
          const jobsArray = Array.isArray(data) ? data : [];
          setJobs(jobsArray);
          setFilteredJobs(jobsArray);
        } else {
          toast.error("Failed to load job suggestions");
        }
      } catch (error) {
        console.error("Network error:", error);
        toast.error("Network error while loading jobs");
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobData();
  }, []);

  // Filter and search functionality
  useEffect(() => {
    let filtered = jobs;

    if (searchTerm) {
      filtered = filtered.filter(job => 
        job.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.skills?.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (selectedLocation) {
      filtered = filtered.filter(job => 
        job.location?.toLowerCase().includes(selectedLocation.toLowerCase())
      );
    }

    if (selectedType) {
      filtered = filtered.filter(job => job.type === selectedType);
    }

    if (selectedExperience) {
      filtered = filtered.filter(job => job.experience === selectedExperience);
    }

    if (sortBy === "recent") {
      filtered = filtered.sort((a, b) => new Date(b.posted) - new Date(a.posted));
    } else if (sortBy === "salary") {
      filtered = filtered.sort((a, b) => {
        const aSalary = parseInt(a.salary?.replace(/[^0-9]/g, '') || '0');
        const bSalary = parseInt(b.salary?.replace(/[^0-9]/g, '') || '0');
        return bSalary - aSalary;
      });
    } else if (sortBy === "match") {
      filtered = filtered.sort((a, b) => (b.match || 0) - (a.match || 0));
    }

    setFilteredJobs(filtered);
  }, [jobs, searchTerm, selectedLocation, selectedType, selectedExperience, sortBy]);

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedLocation("");
    setSelectedType("");
    setSelectedExperience("");
    setSortBy("relevance");
  };

  const jobTypes = ["Full-time", "Part-time", "Contract", "Remote"];
  const experienceLevels = ["Entry Level", "1-2 years", "2-4 years", "4-6 years", "6+ years"];
  const locations = ["San Francisco", "New York", "Los Angeles", "Chicago", "Remote"];

  if (!jstoken) {
    return (
      <div className="p-4">
        <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg shadow-sm p-6 text-center">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
            <HiBriefcase className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Please Sign In</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">You need to be signed in to view suggested jobs</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      {/* Compact Header */}
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg shadow-sm">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-lg">
                <HiOutlineSparkles className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                  Suggested Jobs
                </h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {filteredJobs.length} AI-curated opportunities
                </p>
              </div>
            </div>
            
            {/* Compact Controls */}
            <div className="flex items-center gap-2">
              <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-md p-0.5">
                <button
                  onClick={() => setViewMode("grid")}
                  className={`p-1.5 rounded text-xs transition-colors ${
                    viewMode === "grid" 
                      ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm" 
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  <Grid3X3 className="w-3 h-3" />
                </button>
                <button
                  onClick={() => setViewMode("list")}
                  className={`p-1.5 rounded text-xs transition-colors ${
                    viewMode === "list" 
                      ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm" 
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  <List className="w-3 h-3" />
                </button>
              </div>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-2 py-1.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="relevance">Relevance</option>
                <option value="recent">Recent</option>
                <option value="salary">Salary</option>
                <option value="match">Match</option>
              </select>
            </div>
          </div>

          {/* Compact Search and Filter */}
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-8 pr-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center gap-1 px-3 py-2 rounded-md border text-sm transition-colors ${
                showFilters || selectedLocation || selectedType || selectedExperience
                  ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
                  : "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500"
              }`}
            >
              <SlidersHorizontal className="w-4 h-4" />
              Filters
              {(selectedLocation || selectedType || selectedExperience) && (
                <span className="ml-1 px-1.5 py-0.5 bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-full text-xs">
                  {[selectedLocation, selectedType, selectedExperience].filter(Boolean).length}
                </span>
              )}
              <ChevronDown className={`w-3 h-3 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>

          {/* Compact Filter Panel */}
          {showFilters && (
            <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-3 gap-2">
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="px-2 py-1.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Locations</option>
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>

                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="px-2 py-1.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Types</option>
                  {jobTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>

                <select
                  value={selectedExperience}
                  onChange={(e) => setSelectedExperience(e.target.value)}
                  className="px-2 py-1.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Levels</option>
                  {experienceLevels.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>

              {(selectedLocation || selectedType || selectedExperience) && (
                <div className="mt-2 flex justify-between items-center">
                  <div className="flex flex-wrap gap-1">
                    {selectedLocation && (
                      <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs">
                        <MapPin className="w-2.5 h-2.5" />
                        {selectedLocation}
                      </span>
                    )}
                    {selectedType && (
                      <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs">
                        <Briefcase className="w-2.5 h-2.5" />
                        {selectedType}
                      </span>
                    )}
                    {selectedExperience && (
                      <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs">
                        <Clock className="w-2.5 h-2.5" />
                        {selectedExperience}
                      </span>
                    )}
                  </div>
                  <button
                    onClick={clearFilters}
                    className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                  >
                    Clear all
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Compact Job Results */}
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg shadow-sm">
        <div className="p-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <SmLoader text="Loading jobs..." />
            </div>
          ) : filteredJobs.length > 0 ? (
            <>
              {/* Compact Results Summary */}
              {filteredJobs.length !== jobs.length && (
                <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Showing {filteredJobs.length} of {jobs.length} jobs
                    {searchTerm && <span> for "{searchTerm}"</span>}
                  </div>
                  <button
                    onClick={clearFilters}
                    className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    Show all
                  </button>
                </div>
              )}

              {/* Compact Job Grid */}
              <div className={
                viewMode === "grid" 
                  ? "grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                  : "space-y-3"
              }>
                {filteredJobs.map((job, index) => (
                  <JobCard 
                    iconUrl={job.image} 
                    key={index} 
                    job={job}
                  />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                {searchTerm || selectedLocation || selectedType || selectedExperience ? (
                  <Search className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                ) : (
                  <HiOutlineSparkles className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                )}
              </div>
              
              {searchTerm || selectedLocation || selectedType || selectedExperience ? (
                <>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    No jobs found
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-sm mx-auto mb-4">
                    Try adjusting your search criteria.
                  </p>
                  <button
                    onClick={clearFilters}
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    Clear Filters
                  </button>
                </>
              ) : (
                <>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    AI is finding matches
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 max-w-sm mx-auto mb-4">
                    Analyzing your profile to curate the best opportunities
                  </p>
                  <div className="flex justify-center">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default SuggestedJobs;
