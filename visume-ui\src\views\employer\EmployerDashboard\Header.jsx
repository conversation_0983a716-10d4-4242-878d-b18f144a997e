import React from "react";
import avatar from "assets/img/avatars/avatar4.png";

const Header = ({ empData }) => {
  return (
    <div className="col-span-full bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm lg:col-span-3">
      <div className="flex items-start space-x-4">
        {/* Avatar Section */}
        <div className="flex-shrink-0">
          <img
            src={
              empData?.profile_picture
                ? empData.profile_picture.startsWith("http")
                  ? empData.profile_picture
                  : `${import.meta.env.VITE_APP_HOST}/${empData.profile_picture}`
                : avatar
            }
            alt="User Avatar"
            className="h-14 w-14 rounded-full ring-2 ring-gray-100 dark:ring-gray-700 shadow-sm"
            onError={(e) => {
              if (e.target.src !== avatar) {
                e.target.onerror = null;
                e.target.src = avatar;
              }
            }}
          />
        </div>

        {/* Content Section */}
        <div className="flex-1 min-w-0">
          {/* Name */}
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1.5 truncate">
            {empData?.name || "Loading..."}
          </h3>

          {/* Plan Badge */}
          <div className="mb-2">
            <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-800">
              {empData?.plan_name || "PRO"}
            </span>
          </div>

          {/* Credits Section */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Credits:
            </span>
            <span className="text-lg font-semibold text-gray-900 dark:text-white">
              {empData?.creditsLeft || 0}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;