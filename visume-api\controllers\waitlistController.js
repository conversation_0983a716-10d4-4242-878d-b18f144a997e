// visume-api/controllers/waitlistController.js

const prisma = require('../config/prisma');

exports.createWaitlistEntry = async (req, res) => {
  try {
    const { userType, email, phone, company, industry } = req.body;

    // Validation
    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }
    if (!userType) {
      return res.status(400).json({ error: 'userType is required.' });
    }
    if (userType === 'HIRING' && !company) {
      return res.status(400).json({ error: 'Company is required for HIRING userType.' });
    }

    const entry = await prisma.waitlistEntry.create({
      data: {
        userType,
        email,
        phone,
        company,
        industry,
      },
    });

    return res.status(201).json({ success: true, entry });
  } catch (error) {
    console.error('Waitlist entry error:', error);
    return res.status(500).json({ error: error.message || 'Failed to create waitlist entry.' });
  }
};

exports.checkWaitlistEmail = async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }
    const existing = await prisma.waitlistEntry.findFirst({
      where: { email },
    });
    if (existing) {
      return res.status(200).json({ exists: true });
    } else {
      return res.status(200).json({ exists: false });
    }
  } catch (error) {
    console.error('Check waitlist email error:', error);
    return res.status(500).json({ error: error.message || 'Failed to check waitlist email.' });
  }
};