const ProblemStatementJobSeeker = () => {
  return (
    <section id="introduction" className="py-6 lg:py-8 bg-white relative">
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
        
        .card-grid {
          display: grid;
          gap: 1.25rem;
          margin-top: 1.5rem;
        }
        @media(min-width: 768px) {
          .card-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }
        
        .visume-card {
          background: #ffffff;
          border: 2px solid #dbeafe;
          border-radius: 2rem;
          padding: 0;
          display: flex;
          flex-direction: column;
          position: relative;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          aspect-ratio: 4/5;
          box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12);
        }
        
        .visume-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.04) 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 1;
        }
        
        .visume-card:hover::before {
          opacity: 1;
        }
        
        .visume-card:hover {
          transform: translateY(-12px);
          border-color: #3b82f6;
          box-shadow: 0 24px 48px rgba(59, 130, 246, 0.2);
        }
        
        /* Top Half - Animation Area */
        .animation-half {
          height: 60%;
          width: 100%;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          position: relative;
          overflow: hidden;
          border-radius: 1.8rem 1.8rem 0 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        /* Bottom Half - Text Area */
        .text-half {
          height: 40%;
          width: 100%;
          padding: 1rem 1.5rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          position: relative;
          z-index: 2;
          background: white;
        }
        
        /* Card 1 Visual - Floating resumes - ZOOMED IN */
        .floating-resumes {
          width: 100%;
          height: 100%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: scale(1.3); /* Zoom in effect */
        }

        .resume-doc {
          width: 50px; /* Increased from 40px */
          height: 65px; /* Increased from 50px */
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
          border-radius: 8px; /* Slightly larger radius */
          position: absolute;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          padding: 6px; /* Increased padding */
          box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4); /* Enhanced shadow */
        }
        
        .resume-doc::before {
          content: '';
          width: 80%;
          height: 3px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 2px;
          margin-bottom: 2px;
        }
        
        .resume-doc::after {
          content: '';
          width: 60%;
          height: 2px;
          background: rgba(255, 255, 255, 0.6);
          border-radius: 1px;
        }
        
        .resume-doc:nth-child(1) {
          animation: floatResume1 4s ease-in-out infinite;
          z-index: 3;
        }
        .resume-doc:nth-child(2) {
          animation: floatResume2 4s ease-in-out infinite 0.5s;
          z-index: 2;
        }
        .resume-doc:nth-child(3) {
          animation: floatResume3 4s ease-in-out infinite 1s;
          z-index: 1;
        }
        .resume-doc:nth-child(4) {
          animation: floatResume4 4s ease-in-out infinite 1.5s;
          z-index: 1;
        }
        .resume-doc:nth-child(5) {
          animation: floatResume5 4s ease-in-out infinite 2s;
          z-index: 1;
        }
        
        @keyframes floatResume1 {
          0%, 100% { transform: translate(-20px, -10px) rotate(-5deg) scale(1); opacity: 0.9; }
          50% { transform: translate(-25px, -20px) rotate(-8deg) scale(1.05); opacity: 1; }
        }
        @keyframes floatResume2 {
          0%, 100% { transform: translate(15px, 5px) rotate(3deg) scale(0.95); opacity: 0.8; }
          50% { transform: translate(20px, -5px) rotate(6deg) scale(1); opacity: 1; }
        }
        @keyframes floatResume3 {
          0%, 100% { transform: translate(-5px, 15px) rotate(-2deg) scale(0.9); opacity: 0.7; }
          50% { transform: translate(-10px, 10px) rotate(-4deg) scale(0.95); opacity: 0.9; }
        }
        @keyframes floatResume4 {
          0%, 100% { transform: translate(25px, -5px) rotate(4deg) scale(0.85); opacity: 0.6; }
          50% { transform: translate(30px, -15px) rotate(7deg) scale(0.9); opacity: 0.8; }
        }
        @keyframes floatResume5 {
          0%, 100% { transform: translate(-15px, 20px) rotate(-3deg) scale(0.8); opacity: 0.5; }
          50% { transform: translate(-20px, 15px) rotate(-6deg) scale(0.85); opacity: 0.7; }
        }
        
        /* Card 2 Visual - Enhanced ATS Filter Animation with Profile Icon */
        .ats-filter {
          width: 100%;
          height: 100%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: scale(1.4);
        }

        .ats-system {
          width: 100px;
          height: 70px;
          background: linear-gradient(135deg, rgba(30, 64, 175, 0.9) 0%, rgba(59, 130, 246, 0.9) 100%);
          border-radius: 16px;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.5);
          overflow: hidden;
          backdrop-filter: blur(10px);
          border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .ats-system::before {
          content: 'ATS';
          color: white;
          font-size: 12px;
          font-weight: bold;
          font-family: 'Sora', sans-serif;
          z-index: 2;
          position: relative;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .profile-icon {
          position: absolute;
          left: -35px;
          width: 24px;
          height: 24px;
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(30, 64, 175, 0.9) 100%);
          border-radius: 50%;
          animation: profileFlow 4s ease-in-out infinite;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid rgba(255, 255, 255, 0.3);
          backdrop-filter: blur(5px);
        }

        .profile-icon::before {
          content: '';
          width: 8px;
          height: 8px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          position: absolute;
          top: 3px;
        }

        .profile-icon::after {
          content: '';
          width: 12px;
          height: 8px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 6px 6px 0 0;
          position: absolute;
          bottom: 2px;
        }

        .filter-barrier {
          position: absolute;
          right: 15px;
          width: 4px;
          height: 50px;
          background: linear-gradient(to bottom, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
          border-radius: 2px;
          animation: barrierBlock 4s ease-in-out infinite;
          backdrop-filter: blur(5px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .rejected-indicator {
          position: absolute;
          right: -40px;
          top: 50%;
          transform: translateY(-50%);
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
          animation: rejectPulse 4s ease-in-out infinite;
          opacity: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(5px);
          border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .rejected-indicator::before {
          content: '✕';
          color: white;
          font-size: 10px;
          font-weight: bold;
        }

        @keyframes profileFlow {
          0% { transform: translateX(0) scale(1); opacity: 1; }
          25% { transform: translateX(40px) scale(0.9); opacity: 1; }
          50% { transform: translateX(80px) scale(0.8); opacity: 0.8; }
          60% { transform: translateX(80px) scale(0.8); opacity: 0.3; }
          75% { transform: translateX(40px) scale(0.9); opacity: 0.2; }
          100% { transform: translateX(0) scale(1); opacity: 1; }
        }

        @keyframes barrierBlock {
          0%, 100% { opacity: 0.7; transform: scaleY(1); background: linear-gradient(to bottom, rgba(239, 68, 68, 0.7) 0%, rgba(220, 38, 38, 0.7) 100%); }
          50% { opacity: 1; transform: scaleY(1.3); background: linear-gradient(to bottom, rgba(239, 68, 68, 1) 0%, rgba(220, 38, 38, 1) 100%); }
        }

        @keyframes rejectPulse {
          0%, 50% { opacity: 0; transform: translateY(-50%) scale(0.8); }
          60% { opacity: 1; transform: translateY(-50%) scale(1.2); }
          80% { opacity: 1; transform: translateY(-50%) scale(1); }
          100% { opacity: 0; transform: translateY(-50%) scale(0.8); }
        }
        
        /* Card 3 Visual - Fully blue silhouettes - ZOOMED IN */
        .invisible-crowd {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          height: 100%;
          width: 100%;
          position: relative;
          padding: 20px;
          transform: scale(1.5);
        }

        .person-silhouette {
          width: 32px;
          height: 45px;
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
          border-radius: 16px 16px 0 0;
          position: relative;
          animation: fadeInOut 4s ease-in-out infinite;
          opacity: 0.8;
          box-shadow: 0 0 12px rgba(59, 130, 246, 0.4);
        }

        .person-silhouette::before {
          content: '';
          position: absolute;
          top: -16px;
          left: 50%;
          transform: translateX(-50%);
          width: 16px;
          height: 16px;
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
          border-radius: 50%;
          box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
        }

        .person-silhouette::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 4px;
          height: 4px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          animation: heartbeat 2s ease-in-out infinite;
          box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
        }

        .person-silhouette:nth-child(1) { animation-delay: 0s; }
        .person-silhouette:nth-child(2) { animation-delay: 0.5s; }
        .person-silhouette:nth-child(3) { animation-delay: 1s; }
        .person-silhouette:nth-child(4) { animation-delay: 1.5s; }
        .person-silhouette:nth-child(5) { animation-delay: 2s; }

        @keyframes fadeInOut {
          0%, 100% { opacity: 0.6; transform: scale(0.95); }
          50% { opacity: 1; transform: scale(1.05); }
        }

        @keyframes heartbeat {
          0%, 100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1); }
          50% { opacity: 1; transform: translate(-50%, -50%) scale(1.5); }
        }
        
        .blue-gradient-text {
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        .text-half h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.3;
          font-family: 'Manrope', sans-serif;
        }
        
        .text-half p {
          margin: 0;
          font-size: 0.8rem;
          color: #6b7280;
          line-height: 1.5;
          font-weight: 400;
          font-family: 'Sora', sans-serif;
        }
        
        @media (max-width: 768px) {
          .visume-card {
            aspect-ratio: 5/4;
          }
          .animation-half {
            height: 55%;
          }
          .text-half {
            height: 45%;
            padding: 1rem 1.25rem;
          }
          .text-half h3 {
            font-size: 1rem;
          }
          .text-half p {
            font-size: 0.8rem;
          }
        }
      `}</style>
      <div className="max-w-4xl mx-auto px-6">
        {/* Top badge - Updated to match other sections */}
        <div className="text-center mb-8">
          <div className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm">
            <span style={{ fontFamily: "Sora, sans-serif" }}>Why Visume?</span>
          </div>
        </div>
        {/* Main heading */}
        <h2
          className="text-center text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 tracking-tight leading-tight mb-3"
          style={{ fontFamily: "Manrope, sans-serif" }}
        >
          Job searching feels <span className="blue-gradient-text">frustrating and impersonal</span>
        </h2>
        {/* Animated Cards */}
        <div className="card-grid">
          <div className="visume-card">
            <div className="animation-half">
              <div className="floating-resumes">
                <div className="resume-doc"></div>
                <div className="resume-doc"></div>
                <div className="resume-doc"></div>
                <div className="resume-doc"></div>
                <div className="resume-doc"></div>
              </div>
            </div>
            <div className="text-half">
              <h3>
                Your resume gets <span className="blue-gradient-text">lost in the noise</span>
              </h3>
              <p>You're just another PDF in a pile of hundreds, with no way to show your true potential.</p>
            </div>
          </div>
          <div className="visume-card">
            <div className="animation-half">
              <div className="ats-filter">
                <div className="ats-system">
                  <div className="profile-icon"></div>
                  <div className="filter-barrier"></div>
                  <div className="rejected-indicator"></div>
                </div>
              </div>
            </div>
            <div className="text-half">
              <h3>
                Automated systems <span className="blue-gradient-text">filter you out</span>
              </h3>
              <p>ATS systems reject great candidates based on keyword matching, not actual skills.</p>
            </div>
          </div>
          <div className="visume-card">
            <div className="animation-half">
              <div className="invisible-crowd">
                <div className="person-silhouette"></div>
                <div className="person-silhouette"></div>
                <div className="person-silhouette"></div>
                <div className="person-silhouette"></div>
                <div className="person-silhouette"></div>
              </div>
            </div>
            <div className="text-half">
              <h3>
                Your personality is <span className="blue-gradient-text">invisible</span>
              </h3>
              <p>Employers can't see who you really are, your passion, or how you'd fit with their team.</p>
            </div>
          </div>
        </div>
        {/* Final statement */}
        <div className="mt-12 text-center">
          <p
            className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 tracking-tight leading-tight max-w-5xl mx-auto"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            We're giving you the power to <span className="blue-gradient-text">showcase your authentic self</span> —
            helping you stand out from the crowd and connect with employers who value{" "}
            <span className="blue-gradient-text">real talent</span>.
          </p>
        </div>
      </div>
    </section>
  )
}

export default ProblemStatementJobSeeker
