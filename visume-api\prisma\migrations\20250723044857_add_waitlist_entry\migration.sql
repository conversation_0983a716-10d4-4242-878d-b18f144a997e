/*
  Warnings:

  - Made the column `cand_id` on table `mock_interview` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `mock_interview` DROP FOREIGN KEY `mock_interview_ibfk_1`;

-- AlterTable
ALTER TABLE `mock_interview` MODIFY `cand_id` VARCHAR(20) NOT NULL;

-- CreateTable
CREATE TABLE `WaitlistEntry` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userType` ENUM('JOB_SEEKER', 'HIRING') NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `phone` VARCHAR(191) NULL,
    `company` VARCHAR(191) NULL,
    `industry` VARCHAR(191) NULL,
    `createdAt` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE `mock_interview` ADD CONSTRAINT `mock_interview_ibfk_1` FOREIGN KEY (`cand_id`) REFERENCES `jobseeker`(`cand_id`) ON DELETE CASCADE ON UPDATE RESTRICT;
