import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, TrendingUp, Target, Lightbulb } from "lucide-react";

export default function QuestionDisplay({
  question,
  isSpeaking,
  isListening,
  currentAnalysis,
  isLoading
}) {
  const getStatusInfo = () => {
    if (isSpeaking) {
      return {
        icon: <Bot className="w-5 h-5" />,
        text: "AI Interviewer is speaking",
        color: "from-blue-500 to-purple-500",
        bgColor: "from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20",
        textColor: "text-blue-700 dark:text-blue-300"
      };
    } else if (isListening) {
      return {
        icon: <Mic className="w-5 h-5 animate-pulse" />,
        text: "AI Interviewer is listening",
        color: "from-green-500 to-emerald-500",
        bgColor: "from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20",
        textColor: "text-green-700 dark:text-green-300"
      };
    } else {
      return {
        icon: <Clock className="w-5 h-5" />,
        text: "AI Interviewer is waiting",
        color: "from-amber-500 to-orange-500",
        bgColor: "from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20",
        textColor: "text-amber-700 dark:text-amber-300"
      };
    }
  };

  const statusInfo = getStatusInfo();

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm overflow-hidden">
        <div className="text-center">
          <div className="mb-4">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
          </div>
          <h2 className="mb-2 text-xl font-semibold text-blue-700 dark:text-blue-300">
            Loading Next Question
          </h2>
          <p className="text-blue-600 dark:text-blue-400">
            Please wait...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm overflow-hidden h-full flex flex-col">
      {/* AI Status Header */}
      <div className={`bg-gradient-to-r ${statusInfo.bgColor} border-b border-gray-200 dark:border-gray-800 flex-shrink-0`}>
        <div className="p-3">
          <div className="flex items-center gap-2">
            <div className={`p-1.5 bg-gradient-to-r ${statusInfo.color} rounded-lg`}>
              <div className="text-white">
                {statusInfo.icon}
              </div>
            </div>
            <div className="flex-1">
              <h2 className={`text-base font-semibold ${statusInfo.textColor}`}>
                {statusInfo.text}
              </h2>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {isSpeaking ? "Listen carefully to the question" : isListening ? "Your response is being recorded" : "Ready for your answer"}
              </p>
            </div>
            {(isSpeaking || isListening) && (
              <div className="ml-auto">
                <div className="flex space-x-1">
                  <div className={`w-1.5 h-1.5 rounded-full animate-pulse ${isSpeaking ? 'bg-blue-500' : 'bg-green-500'}`}></div>
                  <div className={`w-1.5 h-1.5 rounded-full animate-pulse ${isSpeaking ? 'bg-blue-500' : 'bg-green-500'}`} style={{animationDelay: '0.2s'}}></div>
                  <div className={`w-1.5 h-1.5 rounded-full animate-pulse ${isSpeaking ? 'bg-blue-500' : 'bg-green-500'}`} style={{animationDelay: '0.4s'}}></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Question Content */}
      <div className="p-4 flex-1 overflow-y-auto">
        <div className="mb-4">
          <div className="flex items-start gap-2 mb-3">
            <div className="p-1.5 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-lg flex-shrink-0">
              <Sparkles className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">
                Interview Question
              </h3>
              <p className="text-lg font-medium text-gray-900 dark:text-white leading-snug">
                {question}
              </p>
            </div>
          </div>
        </div>

        {/* Analysis Feedback Section */}
        {currentAnalysis && (
          <div className="space-y-3">
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div className="flex items-center gap-2 mb-3">
                <div className="p-1.5 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-lg">
                  <TrendingUp className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-base font-semibold text-gray-900 dark:text-white">
                  Response Analysis
                </h3>
              </div>
              
              {/* Technical and Communication Analysis */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Target className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Technical Understanding</h4>
                  </div>
                  <p className="text-xs text-blue-800 dark:text-blue-200 leading-relaxed">
                    {currentAnalysis.technical_accuracy}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Mic className="w-3 h-3 text-green-600 dark:text-green-400" />
                    <h4 className="text-sm font-medium text-green-900 dark:text-green-100">Communication</h4>
                  </div>
                  <p className="text-xs text-green-800 dark:text-green-200 leading-relaxed">
                    {currentAnalysis.communication}
                  </p>
                </div>
              </div>

              {/* Strengths Section */}
              {currentAnalysis.strengths && currentAnalysis.strengths.length > 0 && (
                <div className="mb-4">
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="w-3 h-3 text-green-600 dark:text-green-400" />
                      <h4 className="text-sm font-medium text-green-900 dark:text-green-100">Strengths</h4>
                    </div>
                    <ul className="space-y-1">
                      {currentAnalysis.strengths.map((strength, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-xs text-green-800 dark:text-green-200">
                          <div className="w-1 h-1 bg-green-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <span>{strength}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Areas for Improvement */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {currentAnalysis.knowledge_gaps?.length > 0 && (
                  <div className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="w-3 h-3 text-amber-600 dark:text-amber-400" />
                      <h4 className="text-sm font-medium text-amber-900 dark:text-amber-100">Knowledge Gaps</h4>
                    </div>
                    <ul className="space-y-1">
                      {currentAnalysis.knowledge_gaps.map((gap, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-xs text-amber-800 dark:text-amber-200">
                          <div className="w-1 h-1 bg-amber-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <span>{gap}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {currentAnalysis.improvement_suggestions?.length > 0 && (
                  <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Lightbulb className="w-3 h-3 text-purple-600 dark:text-purple-400" />
                      <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100">Improvement Suggestions</h4>
                    </div>
                    <ul className="space-y-1">
                      {currentAnalysis.improvement_suggestions.map((suggestion, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-xs text-purple-800 dark:text-purple-200">
                          <div className="w-1 h-1 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <span>{suggestion}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
