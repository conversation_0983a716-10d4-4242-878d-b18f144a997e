@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	html {
		font-family: 'DM Sans', sans-serif !important;
		font-feature-settings: 'kern' !important;
		-webkit-font-smoothing: antialiased;
		letter-spacing: -0.5px;
	}
}

input.defaultCheckbox::before {
	content: url(../src/assets/svg/checked.svg);
	color: white;
	opacity: 0;
	height: 16px;
	width: 16px;
	position: absolute;
	left: 50%;
	transform: translate(-50%, 0px);
}

input:checked.defaultCheckbox::before {
	opacity: 1;
}

/* Custom Scrollbar Styles */
.scrollbar-thin {
	scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
	border-radius: 6px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
	border-radius: 6px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
	background-color: #d1d5db;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
	background-color: #4b5563;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
	background-color: #f3f4f6;
}

.scrollbar-track-gray-800::-webkit-scrollbar-track {
	background-color: #1f2937;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
	background-color: #9ca3af;
}

/* Loading animations */
@keyframes shimmer {
	0% {
		background-position: -200px 0;
	}
	100% {
		background-position: calc(200px + 100%) 0;
	}
}

.animate-shimmer {
	animation: shimmer 2s linear infinite;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200px 100%;
}

/* Hover effects */
.hover-lift {
	transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
	transform: translateY(-2px);
}

/* Glass effect */
.glass-effect {
	backdrop-filter: blur(10px);
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Touch-friendly interactions */
.touch-manipulation {
	touch-action: manipulation;
}

/* Responsive utilities for interview components */
@media (max-height: 600px) {
	.interview-container {
		min-height: calc(100vh - 1rem);
	}

	.code-editor-mobile {
		max-height: 40vh;
		min-height: 200px;
	}
}

@media (max-height: 500px) {
	.code-editor-mobile {
		max-height: 35vh;
		min-height: 180px;
	}
}

/* Ensure buttons are always accessible on small screens */
@media (max-height: 700px) {
	.answer-input-footer {
		position: sticky;
		bottom: 0;
		z-index: 10;
	}
}

/* Popup animation for Create Visume */

/* Page transition animation */
.page-transition {
  opacity: 0;
  transform: scale(0.98) translateY(30px);
  filter: blur(8px);
  animation: pageBetterFadeIn 0.6s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

@keyframes pageBetterFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.98) translateY(30px);
    filter: blur(8px);
  }
  60% {
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}